import asyncio
import time
from fastapi import FastAPI
import pytest

from src.middleware.context_middleware import ContextMiddleware
from src.middleware.logging_middleware import LoggingMiddleware
from src.middleware.caching_middleware import CachingMiddleware
from src.middleware.rate_limiting_middleware import RateLimitingMiddleware

@pytest.fixture
def test_app_with_caching():
    """Create a test FastAPI app with caching middleware."""

    app = FastAPI()

    # Add caching middleware with short TTL for testing
    app.add_middleware(
        CachingMiddleware,
        default_ttl=60,  # 1 minute for testing
        enable_caching=True,
        enable_etag=True,
        enable_user_specific_caching=False,
        max_cache_size=100,
    )

    @app.get("/test")
    async def test_endpoint():
        return {"message": "test", "timestamp": time.time()}

    @app.get("/docs")
    async def docs_endpoint():
        return {"docs": "content"}

    @app.post("/test")
    async def test_post_endpoint():
        return {"message": "post response"}

    return app

@pytest.fixture
def test_app_with_logging():
    """Create a test FastAPI app with logging middleware."""

    app = FastAPI()

    # Add logging middleware
    app.add_middleware(
        LoggingMiddleware,
        enable_request_logging=True,
        enable_response_logging=True,
        log_request_body=False,
        log_response_body=False,
    )

    @app.get("/test")
    async def test_endpoint():
        return {"message": "test"}

    @app.get("/error")
    async def error_endpoint():
        raise ValueError("Test error")

    return app

@pytest.fixture
def test_app_with_full_stack():
    """Create a FastAPI app with the complete middleware stack."""
    app = FastAPI(title="Test App with Full Middleware Stack")

    # Add middleware in the same order as app.py
    # 1. Context middleware (first to establish context)
    app.add_middleware(
        ContextMiddleware,
        enable_request_id=True,
        enable_user_context=True,
        enable_locale_detection=True,
        enable_timing=True,
        default_locale="en",
    )

    # 2. Logging middleware (logs with context)
    app.add_middleware(
        LoggingMiddleware,
        enable_request_logging=True,
        enable_response_logging=True,
        enable_error_logging=True,
        enable_performance_logging=True,
        log_request_body=False,
        log_response_body=False,
        exclude_health_checks=True,
    )

    # 3. Caching middleware (caches before rate limiting)
    app.add_middleware(
        CachingMiddleware,
        default_ttl=60,  # Short TTL for testing
        enable_caching=True,
        enable_etag=True,
        enable_user_specific_caching=False,  # Simplified for testing
        max_cache_size=100,
    )

    # 4. Rate limiting middleware (advanced rate limiting)
    app.add_middleware(
        RateLimitingMiddleware,
        default_requests_per_minute=30,  # Low limit for testing
        default_burst_size=5,
        enable_per_ip_limiting=True,
        enable_per_user_limiting=False,  # Simplified for testing
        enable_endpoint_specific_limits=False,  # Simplified for testing
    )

    # Test endpoints
    @app.get("/test")
    async def test_endpoint():
        return {"message": "test", "timestamp": time.time()}

    @app.get("/slow")
    async def slow_endpoint():
        # Simulate slow endpoint for performance testing
        await asyncio.sleep(0.1)
        return {"message": "slow response"}

    @app.get("/error")
    async def error_endpoint():
        raise ValueError("Test error")

    @app.get("/health")
    async def health_endpoint():
        return {"status": "ok"}

    @app.post("/data")
    async def post_endpoint(data: dict = None):
        return {"received": data or {}}

    return app
