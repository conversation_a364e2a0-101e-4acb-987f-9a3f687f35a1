# src/core/enums/__init__.py
"""
Enumeration classes for Ultimate Electrical Designer.

This module defines enumeration classes used for calculations,
data input/output, electrical design, standards compliance,
system operations, and user/project management within the
Ultimate Electrical Designer backend application. It provides
standardized choices for various parameters and statuses.
"""
from .common_enums import (
    InstallationEnvironment,
    UnitOfMeasure,
)

from .electrical_enums import (
    ComponentCategoryType,
    ComponentType,
    ComponentFunctionalCategory,
    ElectricalNodeType,
    CableInstallationMethod,
    CircuitType,
    LoadType,
    LoadCriticality,
    ElectricalCableType,
    ElectricalInsulationType,
    ConductorMaterial,
    VoltageLevel,
    CableSelectionCriteria,
    ProtectionDeviceType,
    SwitchboardFunction,
    FeederType,
)

from .heat_tracing_enums import (
    HeatingMethodType,
    HTCircuitApplicationType,
    HTSensorType,
    HeatTracingCableCategory,
)

from .mechanical_enums import (
    PipeMaterialType,
    ThermalInsulationType,
    TankType,
    SupportType,
    TankAccessoryType,
    PipeSchedule,
    ValveType,
    SoilType,
)

from .calculation_enums import (
    CalculationType,
    CalculationStatus,
    OptimizationObjective,
)

from .data_io_enums import (
    FileFormat,
    MappingDataType,
    DomainDataType,
    ImportStatus,
    ParseStatus,
    MappingType,
    ReportDocumentType,
    TemplateCategory,
    RenderingEngine,
    ReportStatus,
    ReportPriority,
    TrendPeriod,
)

from .standards_enums import (
    TemperatureClass,
    EngineeringStandard,
    ComplianceLevel,
    ATEXZone,
    ATEXGasGroup,
    ATEXProtectionConcept,
)

from .system_enums import (
    ErrorSeverity,
    ErrorContext,
    MonitoringContext,
    MetricType,
    ValidationSeverity,
    ValidationType,
    ValidationResult,
    SecurityLevel,
)

from .project_management_enums import (
    EventType,
    EntityType,
    ProjectStatus,
    UserRole,
    BOMStatus,
    BOMItemStatus,
    ProcurementStatus,
    TaskStatus,
    MilestoneStatus,
)


__all__ = [
    # common_enums
    "InstallationEnvironment",
    "UnitOfMeasure",

    # electrical_enums
    "ComponentCategoryType",
    "ComponentType",
    "ComponentFunctionalCategory",
    "ElectricalNodeType",
    "CableInstallationMethod",
    "CircuitType",
    "LoadType",
    "LoadCriticality",
    "ElectricalCableType",
    "ElectricalInsulationType",
    "ConductorMaterial",
    "VoltageLevel",
    "CableSelectionCriteria",
    "ProtectionDeviceType",
    "SwitchboardFunction",
    "FeederType",

    # heat_tracing_enums
    "HeatingMethodType",
    "HTCircuitApplicationType",
    "HTSensorType",
    "HeatTracingCableCategory",

    # mechanical_enums
    "PipeMaterialType",
    "ThermalInsulationType",
    "TankType",
    "SupportType",
    "TankAccessoryType",
    "PipeSchedule",
    "ValveType",
    "SoilType",

    # calculations_enums
    "CalculationType",
    "CalculationStatus",
    "OptimizationObjective",

    # data_io_enums
    "FileFormat",
    "MappingDataType",
    "DomainDataType",
    "ImportStatus",
    "ParseStatus",
    "MappingType",
    "ReportDocumentType",
    "TemplateCategory",
    "RenderingEngine",
    "ReportStatus",
    "ReportPriority",
    "TrendPeriod",

    # standards_enums
    "TemperatureClass",
    "EngineeringStandard",
    "ComplianceLevel",
    "ATEXZone",
    "ATEXGasGroup",
    "ATEXProtectionConcept",

    # system_enums
    "ErrorSeverity",
    "ErrorContext",
    "MonitoringContext",
    "MetricType",
    "ValidationSeverity",
    "ValidationType",
    "ValidationResult",
    "SecurityLevel",

    # project_management_enums
    "EventType",
    "EntityType",
    "ProjectStatus",
    "UserRole",
    "BOMStatus",
    "BOMItemStatus",
    "ProcurementStatus",
    "TaskStatus",
    "MilestoneStatus",
]