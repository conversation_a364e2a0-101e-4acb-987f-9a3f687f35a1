# src/core/enums/project_management_enums.py
"""
This module defines enumeration types essential for managing project
lifecycle, user roles, material procurement, and task tracking within the
Ultimate Electrical Designer application. It includes classifications for
project statuses, user roles, Bill of Materials (BOM) statuses,
procurement stages, and general event types for auditing. These enums provide
the structured vocabulary necessary for effective project oversight and collaboration.
"""
from enum import Enum

class EventType(Enum):
    """
    Types of events for activity logging and auditing.
    """
    PROJECT_CREATED = "Project Created"
    PROJECT_UPDATED = "Project Updated"
    PROJECT_DELETED = "Project Deleted"
    COMPONENT_ADDED = "Component Added"
    COMPONENT_UPDATED = "Component Updated"
    COMPONENT_DELETED = "Component Deleted"
    CIRCUIT_CREATED = "Circuit Created"
    CIRCUIT_UPDATED = "Circuit Updated"
    CIRCUIT_DELETED = "Circuit Deleted"
    CALCULATION_INITIATED = "Calculation Initiated"
    CALCULATION_COMPLETED = "Calculation Completed"
    CALCULATION_FAILED = "Calculation Failed"
    REPORT_GENERATED = "Report Generated"
    DATA_IMPORTED = "Data Imported"
    DATA_EXPORTED = "Data Exported"
    USER_LOGIN = "User Login"
    USER_LOGOUT = "User Logout"
    USER_CREATED = "User Created"
    USER_UPDATED = "User Updated"
    USER_DELETED = "User Deleted"
    PERMISSION_CHANGED = "Permission Changed"
    SYSTEM_ERROR = "System Error" # General error event type
    VALIDATION_TRIGGERED = "Validation Triggered"
    VALIDATION_COMPLETED = "Validation Completed"
    BOM_GENERATED = "BOM Generated"
    PURCHASE_ORDER_ISSUED = "Purchase Order Issued"
    ITEM_RECEIVED = "Item Received"

class EntityType(Enum):
    """
    Types of entities referenced in logs or project data.
    """
    PROJECT = "Project"
    COMPONENT = "Component"
    CIRCUIT = "Circuit"
    USER = "User"
    REPORT = "Report"
    CALCULATION = "Calculation"
    BOM = "Bill of Materials"
    TASK = "Task"
    MILESTONE = "Milestone"
    DOCUMENT = "Document"
    SUPPLIER = "Supplier"
    CLIENT = "Client"
    SYSTEM = "System" # Generic system entity

class ProjectStatus(Enum):
    """
    Overall status of a project.
    """
    DRAFT = "Draft"
    ACTIVE = "Active"
    ON_HOLD = "On Hold"
    COMPLETED = "Completed"
    CANCELLED = "Cancelled"
    ARCHIVED = "Archived" # For long-term storage
    PLANNING = "Planning" # Initial phase
    DESIGN = "Design In Progress"
    PROCUREMENT = "Procurement In Progress"
    CONSTRUCTION = "Construction In Progress"
    COMMISSIONING = "Commissioning"

class UserRole(Enum):
    """
    Roles for users within the application.
    """
    ADMIN = "Administrator"
    PROJECT_MANAGER = "Project Manager"
    LEAD_ELECTRICAL_ENGINEER = "Lead Electrical Engineer"
    ELECTRICAL_DESIGNER = "Electrical Designer"
    MECHANICAL_ENGINEER = "Mechanical Engineer"
    INSTRUMENTATION_ENGINEER = "Instrumentation Engineer"
    CAD_OPERATOR = "CAD Operator"
    VIEWER = "Viewer"
    GUEST = "Guest"
    CLIENT = "Client"
    SUPPLIER = "Supplier"

class BOMStatus(Enum):
    """
    Status of a Bill of Materials.
    """
    DRAFT = "Draft"
    UNDER_REVIEW = "Under Review"
    APPROVED = "Approved"
    RELEASED = "Released for Procurement"
    OBSOLETE = "Obsolete"
    REVISED = "Revised" # New: indicates a new version exists

class BOMItemStatus(Enum):
    """
    Status of an individual item within a Bill of Materials.
    """
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    DISCONTINUED = "Discontinued"
    SUBSTITUTE_AVAILABLE = "Substitute Available"
    PENDING_APPROVAL = "Pending Approval"
    ORDERED = "Ordered" # New: for procurement tracking
    RECEIVED = "Received"
    INSTALLED = "Installed"

class ProcurementStatus(Enum):
    """
    Status of procurement for components or equipment.
    """
    NOT_ORDERED = "Not Ordered"
    REQUESTED = "Requested (Internal)"
    QUOTE_PENDING = "Quote Pending"
    QUOTE_RECEIVED = "Quote Received"
    ORDER_PLACED = "Order Placed"
    IN_TRANSIT = "In Transit"
    PARTIALLY_RECEIVED = "Partially Received"
    COMPLETELY_RECEIVED = "Completely Received"
    CANCELLED = "Cancelled"
    ON_HOLD = "On Hold"

class TaskStatus(Enum):
    """
    Status of individual tasks within a project.
    """
    NOT_STARTED = "Not Started"
    IN_PROGRESS = "In Progress"
    ON_HOLD = "On Hold"
    COMPLETED = "Completed"
    BLOCKED = "Blocked"
    OVERDUE = "Overdue"
    REVIEW_PENDING = "Review Pending" # New for workflow
    APPROVED = "Approved"

class MilestoneStatus(Enum):
    """
    Status of project milestones.
    """
    PLANNED = "Planned"
    ON_TRACK = "On Track"
    AT_RISK = "At Risk"
    DELAYED = "Delayed"
    COMPLETED = "Completed"
    CANCELLED = "Cancelled"