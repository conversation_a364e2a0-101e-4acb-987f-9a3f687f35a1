# tests/unit/test_errors/test_unified_error_handler.py
"""
Comprehensive tests for unified error handling system.

This module tests the unified error handler including exception handling, and error tracking.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

import pytest
from fastapi import Request, HTTPException

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    ErrorContext,
    ErrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    get_unified_error_handler,
    handle_service_errors,
    handle_repository_errors,
    handle_calculation_errors,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    DataValidationError,
    InvalidInputError,
    ServiceError,
    DatabaseError,
    CalculationError,
    DuplicateEntryError,
)

pytestmark = [pytest.mark.unit]

class TestUnifiedErrorHandler:
    """Test suite for UnifiedErrorHandler class."""

    def test_unified_error_handler_initialization_defaults(self):
        """Test UnifiedErrorHandler initialization with defaults."""
        handler = UnifiedErrorHandler()

        assert handler.enable_debug_mode is False
        assert handler.include_stack_trace is False
        assert handler.max_error_message_length == 1000
        assert handler.enable_error_tracking is True
        assert handler.error_counts == {}
        assert handler.error_history == []

    def test_unified_error_handler_initialization_custom(self):
        """Test UnifiedErrorHandler initialization with custom values."""
        handler = UnifiedErrorHandler(
            enable_debug_mode=True,
            include_stack_trace=True,
            max_error_message_length=500,
            enable_error_tracking=False,
        )

        assert handler.enable_debug_mode is True
        assert handler.include_stack_trace is True
        assert handler.max_error_message_length == 500
        assert handler.enable_error_tracking is False

    def test_handle_application_exception(self):
        """Test handling BaseApplicationException."""
        handler = UnifiedErrorHandler()
        exception = NotFoundError(
            code="TEST_404",
            detail="Resource not found",
            category="ClientError",
            status_code=404,
        )
        context = ErrorContext.API

        result = handler.handle_exception(exception, context)

        assert isinstance(result, ErrorHandlingResult)
        assert result.http_status_code == 404
        assert result.error_response.detail == "Resource not found"
        assert result.should_log is True
        assert result.log_level == logging.WARNING  # NotFoundError gets WARNING level

    def test_handle_application_exception_with_request(self):
        """Test handling exception with request context."""
        handler = UnifiedErrorHandler()
        exception = ServiceError("Service failed")
        context = ErrorContext.SERVICE

        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/api/v1/test"

        result = handler.handle_exception(exception, context, mock_request)

        assert result.context_data["request_path"] == "/api/v1/test"
        assert result.context_data["exception_type"] == "ServiceError"
        assert result.context_data["context"] == "Service Layer"

    def test_handle_application_exception_with_additional_context(self):
        """Test handling exception with additional context."""
        handler = UnifiedErrorHandler()
        exception = DataValidationError({"field": ["error"]})
        context = ErrorContext.API
        additional_context = {"user_id": "123", "operation": "create"}

        result = handler.handle_exception(
            exception, context, additional_context=additional_context
        )

        assert result.context_data["user_id"] == "123"
        assert result.context_data["operation"] == "create"

    def test_handle_generic_exception(self):
        """Test handling generic Python exception."""
        handler = UnifiedErrorHandler()
        exception = RuntimeError("Unexpected runtime error")
        context = ErrorContext.SERVICE

        result = handler.handle_exception(exception, context)

        assert result.http_status_code == 500
        assert "An unexpected internal error occurred" in result.error_response.detail
        assert result.log_level == logging.ERROR

    def test_handle_exception_debug_mode(self):
        """Test handling exception in debug mode."""
        handler = UnifiedErrorHandler(enable_debug_mode=True, include_stack_trace=True)
        exception = RuntimeError("Debug error")
        context = ErrorContext.API

        result = handler.handle_exception(exception, context)

        # In debug mode, should include more details
        assert "Debug error" in result.error_response.detail

    def test_error_tracking_enabled(self):
        """Test error tracking when enabled."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)
        exception = NotFoundError(code="TEST_404", detail="Not found")
        context = ErrorContext.API

        initial_count = len(handler.error_history)

        handler.handle_exception(exception, context)

        # Should track the error
        assert len(handler.error_history) == initial_count + 1
        assert "API:NotFoundError" in handler.error_counts
        assert handler.error_counts["API:NotFoundError"] == 1

    def test_error_tracking_disabled(self):
        """Test error tracking when disabled."""
        handler = UnifiedErrorHandler(enable_error_tracking=False)
        exception = ServiceError("Service error")
        context = ErrorContext.SERVICE

        initial_count = len(handler.error_history)

        handler.handle_exception(exception, context)

        # Should not track the error
        assert len(handler.error_history) == initial_count
        assert len(handler.error_counts) == 0

    def test_get_error_statistics(self):
        """Test get_error_statistics method."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)

        # Add some test errors
        handler.handle_exception(
            NotFoundError(code="404", detail="Not found"), ErrorContext.API
        )
        handler.handle_exception(ServiceError("Service error"), ErrorContext.SERVICE)
        handler.handle_exception(
            NotFoundError(code="404", detail="Another not found"), ErrorContext.API
        )

        stats = handler.get_error_statistics()

        assert "error_counts" in stats
        assert "recent_errors" in stats
        assert "error_types" in stats
        assert stats["error_counts"]["total"] == 3
        assert stats["error_counts"]["API:NotFoundError"] == 2
        assert stats["error_counts"]["Service Layer:ServiceError"] == 1
        assert len(stats["recent_errors"]) == 3
        assert "NotFoundError" in stats["error_types"]
        assert "ServiceError" in stats["error_types"]

    def test_clear_error_history(self):
        """Test clear_error_history method."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)

        # Add some errors
        handler.handle_exception(ServiceError("Error 1"), ErrorContext.SERVICE)
        handler.handle_exception(ServiceError("Error 2"), ErrorContext.SERVICE)

        assert len(handler.error_history) == 2
        assert len(handler.error_counts) == 1

        handler.clear_error_history()

        assert len(handler.error_history) == 0
        assert len(handler.error_counts) == 0

    def test_error_context_manager(self):
        """Test error_context context manager."""
        handler = UnifiedErrorHandler()

        # Test successful operation
        with handler.error_context(ErrorContext.SERVICE, "test_operation"):
            result = "success"

        # Should complete without issues
        assert result == "success"

    def test_error_context_manager_with_exception(self):
        """Test error_context context manager with exception."""
        handler = UnifiedErrorHandler()

        with pytest.raises(HTTPException) as exc_info:
            with handler.error_context(ErrorContext.SERVICE, "failing_operation"):
                raise RuntimeError("Test error")

        # Should convert to HTTPException
        assert exc_info.value.status_code == 500
        assert "An unexpected internal error occurred" in str(exc_info.value.detail)

    def test_error_context_manager_with_application_exception(self):
        """Test error_context context manager with application exception."""
        handler = UnifiedErrorHandler()

        with pytest.raises(HTTPException) as exc_info:
            with handler.error_context(ErrorContext.API, "api_operation"):
                raise NotFoundError(
                    code="404",
                    detail="Resource not found",
                    category="ClientError",
                    status_code=404,
                )

        assert exc_info.value.status_code == 404
        assert "Resource not found" in str(exc_info.value.detail)

    def test_error_context_manager_with_additional_context(self):
        """Test error_context context manager with additional context."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)
        additional_context = {"user_id": "123"}

        with pytest.raises(HTTPException):
            with handler.error_context(
                ErrorContext.SERVICE, "test_op", additional_context
            ):
                raise ServiceError("Test service error")

        # Check that context was recorded
        assert len(handler.error_history) == 1
        error_record = handler.error_history[0]
        assert error_record["context_data"]["user_id"] == "123"
        assert error_record["context_data"]["operation"] == "test_op"

    def test_message_length_truncation(self):
        """Test error message length truncation."""
        handler = UnifiedErrorHandler(max_error_message_length=50)
        long_message = "A" * 100  # 100 character message
        exception = ServiceError(long_message)

        result = handler.handle_exception(exception, ErrorContext.SERVICE)

        # Message should be truncated
        assert len(result.error_response.detail) <= 50

    def test_different_log_levels_for_exception_types(self):
        """Test different log levels for different exception types."""
        handler = UnifiedErrorHandler()

        # Test WARNING level exceptions
        warning_exceptions = [
            DataValidationError({}),
            InvalidInputError("Invalid input"),
            NotFoundError(code="404", detail="Not found"),
        ]

        for exception in warning_exceptions:
            result = handler.handle_exception(exception, ErrorContext.API)
            assert result.log_level == logging.WARNING

        # Test ERROR level exceptions
        error_exceptions = [
            DatabaseError("DB error"),
            ServiceError("Service error"),
        ]

        for exception in error_exceptions:
            result = handler.handle_exception(exception, ErrorContext.SERVICE)
            assert result.log_level == logging.ERROR

    def test_global_instance_access(self):
        """Test global unified_error_handler instance."""
        assert unified_error_handler is not None
        assert isinstance(unified_error_handler, UnifiedErrorHandler)

    def test_get_unified_error_handler_dependency(self):
        """Test get_unified_error_handler dependency function."""
        handler = get_unified_error_handler()

        assert handler is not None
        assert isinstance(handler, UnifiedErrorHandler)
        assert handler is unified_error_handler  # Should return the same instance
