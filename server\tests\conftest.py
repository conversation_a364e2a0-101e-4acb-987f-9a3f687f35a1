# tests/conftest.py - Global test configuration
import os
import sys
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

# Add server to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
sys.path.insert(0, server_path)

from src.core.database.dependencies import get_db
from src.core.models.base import Base
from tests.fixtures.user_fixtures import user_token

# Test database configuration
TEST_DATABASE_URL = "sqlite:///./test_app.db"

@pytest.fixture(scope="session")
def engine():
    """Create test database engine."""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
            "isolation_level": None,
        },
        poolclass=StaticPool,
        echo=False  # Set to True for SQL debugging
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(engine) -> Generator[Session, None, None]:
    """Create database session for each test."""
    TestingSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
        class_=Session  # Use SQLAlchemy 2.0 Session class
    )

    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.rollback()
        session.close()

        # Clean up all data after each test to ensure isolation
        with engine.connect() as conn:
            # Start a transaction for cleanup
            trans = conn.begin()
            try:
                # Delete all data from all tables in reverse dependency order
                for table in reversed(Base.metadata.sorted_tables):
                    conn.execute(table.delete())
                trans.commit()
            except Exception:
                trans.rollback()
                raise

@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create test client with database session override."""
    # Create a test app without lifespan events
    from fastapi import FastAPI
    from src.api.main_router import create_main_api_router

    # Create a minimal test app
    test_app = FastAPI(title="Test App")

    # Add the API routes with the same prefix as the main app
    api_router = create_main_api_router()
    test_app.include_router(api_router, prefix="/api")

    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    test_app.dependency_overrides[get_db] = override_get_db

    with TestClient(test_app) as test_client:
        yield test_client

    test_app.dependency_overrides.clear()

@pytest.fixture(scope="function")
def authenticated_client(client: TestClient, user_token) -> TestClient:
    """Create authenticated test client."""
    
    # Set authorization header
    client.headers.update({"Authorization": f"Bearer {user_token}"})
    
    return client

# Pytest markers for test categorization
pytest_plugins = ["pytest_asyncio"]

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual components"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for component interaction"
    )
    config.addinivalue_line(
        "markers", "api: API endpoint tests"
    )
    config.addinivalue_line(
        "markers", "database: Database operation tests"
    )
    config.addinivalue_line(
        "markers", "calculations: Engineering calculation tests"
    )
    config.addinivalue_line(
        "markers", "standards: Standards compliance tests"
    )
    config.addinivalue_line(
        "markers", "security: Security validation tests"
    )
    config.addinivalue_line(
        "markers", "performance: Performance benchmarking tests"
    )
    config.addinivalue_line(
        "markers", "slow: Tests that take longer than 1 second"
    )

def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:

        # Mark database tests
        if "database" in str(item.fspath) or "db_session" in item.fixturenames:
            item.add_marker(pytest.mark.database)

        # Mark repository tests
        if "repositories" in str(item.fspath):
            item.add_marker(pytest.mark.repository)
            item.add_marker(pytest.mark.database)
            item.add_marker(pytest.mark.unit)
        
        # Mark API tests
        if "api" in str(item.fspath):
            item.add_marker(pytest.mark.api)

        # Mark calculation tests
        if "calculations" in str(item.fspath):
            item.add_marker(pytest.mark.calculations)

        # Mark service tests
        if "services" in str(item.fspath):
            item.add_marker(pytest.mark.service)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)

        # Mark integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
            
        # Mark security tests
        if "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)
            
        # Mark performance tests
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        
        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)
        else:
            # Default to unit tests
            item.add_marker(pytest.mark.unit)