# backend/core/database/engine.py
"""Database Engine Management

This module handles the creation and configuration of SQLAlchemy engines
with automatic fallback from SQL Server to SQLite based on environment
and connection availability.
"""

import sqlite3
from typing import Any

from sqlalchemy import Engine
from sqlalchemy import create_engine as sqlalchemy_create_engine
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import StaticPool
from sqlalchemy.engine import Connection

try:
    from src.config.logging_config import logger
    from src.config.settings import settings
except ImportError:
    from src.config.logging_config import logger
    from src.config.settings import settings
try:
    from src.core.errors.exceptions import DatabaseError
except ImportError:
    from src.core.errors.exceptions import DatabaseError

from src.core.errors.unified_error_handler import handle_database_errors

# Global engine instance
_engine: Engine | None = None


def _configure_sqlite_threading_mode() -> None:
    """Configure SQLite for serialized (thread-safe) mode."""
    try:
        # Enable serialized mode for better thread safety
        # This allows SQLite connections to be shared between threads safely
        sqlite3.threadsafety = 3  # Serialized mode
        logger.debug("SQLite configured for serialized threading mode")
    except Exception as e:
        logger.warning(f"Failed to configure SQLite threading mode: {e}")


@handle_database_errors("engine_creation")
def create_engine(
    database_url: str | None = None, echo: bool = False, force_recreate: bool = False
) -> Engine:
    """Create and configure a SQLAlchemy engine with automatic fallback logic.

    Args:
        database_url: Optional database URL. If None, uses settings.effective_database_url
        echo: Whether to echo SQL statements to stdout
        force_recreate: Whether to force recreation of the engine even if one exists

    Returns:
        Configured SQLAlchemy Engine instance

    Raises:
        RuntimeError: If unable to create any database connection

    """
    global _engine

    if _engine is not None and not force_recreate:
        logger.debug("Returning existing engine instance")
        return _engine

    if database_url is None:
        database_url = settings.effective_database_url

    logger.info(f"Creating database engine for environment: {settings.ENVIRONMENT}")

    # Configure SQLite threading mode if we're using SQLite
    if database_url.startswith("sqlite") or settings.effective_database_url.startswith(
        "sqlite"
    ):
        _configure_sqlite_threading_mode()

    # Try to create engine with the provided URL
    engine = _create_engine_with_fallback(database_url, echo)

    if engine is None:
        error_msg = "Failed to create database engine with any available configuration"
        logger.critical(error_msg)
        raise DatabaseError(
            reason=error_msg,
        )

    _engine = engine
    logger.info(
        f"Database engine created successfully using: {_get_db_type_from_url(engine.url)}"
    )
    return _engine


def _create_engine_with_fallback(database_url: str, echo: bool) -> Engine | None:
    """Create engine with fallback logic from SQL Server to SQLite.

    Args:
        database_url: Primary database URL to try
        echo: Whether to echo SQL statements

    Returns:
        Engine instance or None if all attempts fail

    """
    # First, try the provided database URL
    engine = _try_create_engine(database_url, echo)
    if engine is not None:
        return engine

    logger.warning(f"Primary database connection failed for: {database_url}")

    # If the primary URL failed and it's not SQLite, try SQLite fallback
    if not database_url.startswith("sqlite"):
        logger.warning("Falling back to SQLite")
        sqlite_url = settings._get_sqlite_url()
        engine = _try_create_engine(sqlite_url, echo)
        if engine is not None:
            logger.info("Successfully connected to SQLite fallback database")
            return engine
        else:
            logger.error("SQLite fallback also failed")

    # If we get here, all attempts failed
    logger.error("All database connection attempts failed")
    return None


def _try_create_engine(database_url: str, echo: bool) -> Engine | None:
    """Attempt to create and test a database engine.

    Args:
        database_url: Database URL to try
        echo: Whether to echo SQL statements

    Returns:
        Engine instance if successful, None otherwise

    """
    try:
        # Configure engine based on database type
        engine_kwargs: dict[str, Any] = {
            "echo": echo,
            "future": True,  # Use SQLAlchemy 2.0 style
        }

        # Special configuration for SQLite
        if database_url.startswith("sqlite"):
            engine_kwargs["poolclass"] = StaticPool
            engine_kwargs["connect_args"] = {
                "check_same_thread": False,  # Allow SQLite to be used across threads
                "timeout": 60,  # Increased timeout for database locks under concurrent load
                # Enable WAL mode and other performance optimizations
                "isolation_level": None,  # Enable autocommit mode for better concurrency
            }
            # SQLite with StaticPool doesn't support pool_size and max_overflow
            engine_kwargs["pool_pre_ping"] = True  # Verify connections before use
            engine_kwargs["pool_recycle"] = (
                1800  # More frequent recycling for concurrent load
            )
        else:
            # Production-optimized configuration for PostgreSQL, SQL Server, etc.
            # Base pool size optimized for electrical design workloads
            engine_kwargs["pool_size"] = (
                30  # Optimized for concurrent electrical calculations
            )
            engine_kwargs["max_overflow"] = (
                50  # Higher overflow for peak calculation loads
            )
            engine_kwargs["pool_pre_ping"] = True  # Validate connections before use

            # Connection lifecycle optimization for engineering workloads
            engine_kwargs["pool_recycle"] = (
                3600  # 1 hour - balance between freshness and performance
            )
            engine_kwargs["pool_timeout"] = (
                45  # Increased timeout for complex calculations
            )
            engine_kwargs["pool_reset_on_return"] = (
                "commit"  # Clean state for each request
            )

            # Advanced connection pool optimizations
            engine_kwargs["connect_args"] = {
                # Connection-level optimizations for electrical design workloads
                "application_name": "Ultimate_Electrical_Designer",
                "connect_timeout": 30,  # Connection establishment timeout
                "command_timeout": 300,  # Query execution timeout (5 minutes for complex calculations)
            }

        # Create the engine
        engine = sqlalchemy_create_engine(database_url, **engine_kwargs)

        # Test the connection and configure SQLite-specific settings
        with engine.connect() as conn:
            from sqlalchemy import text

            conn.execute(text("SELECT 1"))

            # Configure SQLite for better concurrency if this is a SQLite database
            if database_url.startswith("sqlite"):
                _configure_sqlite_for_concurrency(conn)

        logger.debug(
            f"Successfully created and tested engine for: {_get_db_type_from_url(engine.url)}"
        )
        return engine

    except Exception as e:
        # Log the error but return None to allow fallback mechanisms
        logger.debug(f"Failed to create engine for {database_url}: {e}")
        return None


def _configure_sqlite_for_concurrency(conn: Connection) -> None:
    """Configure SQLite database for better concurrent performance.

    This function enables:
    - WAL (Write-Ahead Logging) mode for better concurrency
    - Serialized threading mode for thread safety
    - Optimized cache and timeout settings
    """
    from sqlalchemy import text

    try:
        # Enable WAL mode for better concurrency (readers don't block writers)
        logger.info("Configuring SQLite for WAL mode and better concurrency...")

        # Set WAL mode
        result = conn.execute(text("PRAGMA journal_mode=WAL"))
        row = result.fetchone()
        wal_mode = row[0] if row else "unknown"
        logger.info(f"SQLite journal mode set to: {wal_mode}")

        # Set synchronous mode to NORMAL for better performance with WAL
        conn.execute(text("PRAGMA synchronous=NORMAL"))

        # Increase cache size (negative value means KB, positive means pages)
        conn.execute(text("PRAGMA cache_size=-64000"))  # 64MB cache

        # Set busy timeout for better handling of concurrent access
        conn.execute(text("PRAGMA busy_timeout=30000"))  # 30 seconds

        # Enable foreign key constraints
        conn.execute(text("PRAGMA foreign_keys=ON"))

        # Optimize for concurrent access
        conn.execute(text("PRAGMA temp_store=MEMORY"))  # Store temp tables in memory
        conn.execute(text("PRAGMA mmap_size=268435456"))  # 256MB memory-mapped I/O

        # Set WAL autocheckpoint for better performance
        conn.execute(
            text("PRAGMA wal_autocheckpoint=1000")
        )  # Checkpoint every 1000 pages

        logger.info("SQLite concurrency optimizations applied successfully")

    except Exception as e:
        logger.warning(f"Failed to configure SQLite optimizations: {e}")
        # Don't fail the connection if optimizations fail


def _get_db_type_from_url(url: Any) -> str:
    """Extract database type from URL for logging purposes."""
    url_str = str(url)
    if url_str.startswith("sqlite"):
        return "SQLite"
    if url_str.startswith("mssql") or url_str.startswith("sqlserver"):
        return "SQL Server"
    if url_str.startswith("postgresql"):
        return "PostgreSQL"
    if url_str.startswith("mysql"):
        return "MySQL"
    return "Unknown"


@handle_database_errors("engine_retrieval")
def get_engine() -> Engine:
    """Get the current engine instance.

    Returns:
        Current Engine instance

    Raises:
        RuntimeError: If no engine has been created yet

    """
    global _engine
    if _engine is None:
        raise RuntimeError(
            "Database engine not initialized. Call create_engine() first."
        )
    return _engine


def close_engine() -> None:
    """Close the current engine and reset the global instance.

    This function handles disposal errors gracefully to ensure the engine
    is always reset to None, even if disposal fails.
    """
    global _engine
    if _engine is not None:
        try:
            _engine.dispose()
            logger.info("Database engine closed and disposed")
        except Exception as e:
            # Log disposal error but don't raise - we still want to reset the engine
            logger.warning(f"Error during engine disposal: {e}")
        finally:
            # Always reset the engine to None, even if disposal failed
            _engine = None
