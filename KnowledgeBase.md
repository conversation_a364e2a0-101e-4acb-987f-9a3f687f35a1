To effectively complement the established workflows and rules, the knowledge base should be meticulously organized and comprehensive, leveraging the provided `folder-structure.json` to mirror the actual codebase and integrate existing documentation. This ensures that all development activities, including those performed by AI agents, align perfectly with the project's standards and architecture.

### Knowledge Base Structure

```python
docs/
├── ai-agent-team/                                # Documentation specific to the AI Agent Team's operation and collaboration.
│   ├── agent-implementation-guides.md            # Guides for implementing new AI agent functionalities or integrating existing ones.
│   ├── agent-training.md                         # Documentation related to training methodologies and datasets for AI agents.
│   ├── coordination-protocols.md                 # Defines how AI agents coordinate tasks, handovers, and communication.
│   └── quality-assurance.md                      # Details AI agent's role in quality assurance, including automated reviews and testing.
├── architecture/                                 # Central repository for the overall system architecture and design principles.
│   ├── 000-architecture-overview.md              # High-level diagram and explanation of the entire system's architecture, explicitly showing FastAPI as the main gateway.
│   ├── 010-backend-architecture.md               # Detailed documentation of the 5-layer architecture and backend patterns (e.g., Unified Patterns) applied within FastAPI and other services.
│   ├── 020-frontend-architecture-specification.md# In-depth specification for the frontend's architecture, components, and design principles.
│   ├── 030-api-design-principles.md              # Guidelines for designing and implementing APIs: differentiate between external (FastAPI to client) and internal (FastAPI to C-based services) APIs.
│   └── 040-database-schema.md                    # Documentation of the database schema, including tables, relationships, and data types.
├── cad-integrator-service/                       # Documentation specific to the CAD Integrator internal Microservice.
│   ├── README.md                                 # Overview of the CAD Integrator Service (as an internal service).
│   ├── internal-api-spec.md                      # API endpoints and interaction details exposed *to FastAPI*.
│   ├── layers/                                   # Documentation structured by 5-layer architecture for this internal service.
│   │   ├── presentation/                         # (This layer would document API exposed to FastAPI)
│   │   │   ├── controllers/                      # Guide to the controllers handling internal API requests.
│   │   │   └── dtos/                             # DTOs used for internal API communication.
│   │   ├── application/                          # Application-level services for this microservice.
│   │   ├── domain/                               # Core business entities and logic for this microservice.
│   │   ├── infrastructure/                       # External integrations and data access specific to this microservice.
│   │   └── database/                             # Database specifics for this microservice.
│   ├── plugins-guide.md                          # Instructions for developing and integrating CAD plugins.
│   └── services-guide.md                         # Documentation for core internal services.
├── client/                                       # Documentation specific to the frontend client application.
│   ├── README.md                                 # Overview of the client application.
│   ├── architecture/                             # Frontend-specific architectural details.
│   │   ├── app-structure.md                      # Explains the overall application directory and component structure.
│   │   └── routing.md                            # Documentation for client-side routing and navigation.
│   ├── component-library/                        # Documentation for reusable UI components.
│   │   ├── common-components.md                  # Documentation for generic, reusable components.
│   │   ├── domain-components.md                  # Documentation for components specific to certain business domains.
│   │   └── ui-components.md                      # Usage and customization of `shadcn/ui` and other fundamental UI elements.
│   ├── hooks-guide.md                            # Explanations and usage examples for custom React hooks.
│   ├── lib-guide.md                              # Documentation for utility functions, constants, and global configurations.
│   ├── modules/                                  # Documentation for domain-driven frontend modules, mirroring `client/src/modules`.
│   │   ├── cable_sizing/                         # Documentation for the Cable Sizing frontend module.
│   │   │   ├── README.md                         # Overview of the Cable Sizing frontend module.
│   │   │   ├── api-usage.md                      # How the module interacts with the main FastAPI API.
│   │   │   ├── components-guide.md               # Specific components within the Cable Sizing module.
│   │   │   ├── hooks-guide.md                    # Hooks specific to Cable Sizing.
│   │   │   └── types.md                          # TypeScript type definitions for Cable Sizing.
│   │   ├── circuits/                             # Documentation for the Circuits frontend module.
│   │   │   ├── README.md                         # Overview of the Circuits frontend module.
│   │   │   └── ...                               # (Similar detailed documentation structure)
│   │   ├── heat_tracing/                         # Documentation for the Heat Tracing frontend module.
│   │   │   ├── README.md                         # Overview of the Heat Tracing frontend module.
│   │   │   └── ...
│   │   ├── load_calculations/                    # Documentation for the Load Calculations frontend module.
│   │   │   ├── README.md                         # Overview of the Load Calculations frontend module.
│   │   │   └── ...
│   │   ├── projects/                             # Documentation for the Projects frontend module.
│   │   │   ├── README.md                         # Overview of the Projects frontend module.
│   │   │   └── ...
│   │   └── users/                                # Documentation for the Users frontend module.
│   │       ├── README.md                         # Overview of the Users frontend module.
│   │       └── ...
│   ├── services-guide.md                         # Documentation for frontend-specific services (e.g., authentication, error monitoring), primarily interacting with FastAPI.
│   ├── types-guide.md                            # Global and common TypeScript type definitions for the client.
│   └── utils-guide.md                            # Documentation for general frontend utility functions.
├── computation-engine-service/                   # Documentation specific to the Computation Engine internal Microservice.
│   ├── README.md                                 # Overview of the Computation Engine Service (as an internal service).
│   ├── internal-api-spec.md                      # API endpoints and interaction details exposed *to FastAPI*.
│   ├── layers/                                   # Documentation structured by 5-layer architecture for this internal service.
│   │   ├── presentation/                         # (This layer would document API exposed to FastAPI)
│   │   │   ├── controllers/                      # Documentation for individual controllers (e.g., ComputationController.cs)
│   │   │   │   └── computation-controller.md
│   │   │   └── dtos/                             # Documentation for Data Transfer Objects for internal communication.
│   │   ├── application/                          # Application-level services for this microservice.
│   │   ├── domain/                               # Core business entities and logic for this microservice.
│   │   ├── infrastructure/                       # External integrations and data access specific to this microservice.
│   │   └── database/                             # Database specifics for this microservice.
│   ├── unified-patterns/                         # How unified patterns are implemented in this internal service.
│   ├── testing/                                  # Testing strategy for this internal service.
│   └── troubleshooting/                          # Common issues and their resolutions for this internal service.
├── calculations/                                 # Documentation on the engineering calculations used in the system.
│   ├── 001-electrical-calculations.md            # Detailed documentation on general electrical engineering calculations.
│   └── 002-heat-loss-calculations.md             # Detailed documentation on heat loss and thermal analysis calculations.
├── developer-handbooks/                          # Comprehensive guides for developers on various aspects of the project.
│   ├── 001-cover.md                              # Cover or introductory page for the Developer Handbook.
│   ├── 010-development-workflow.md               # Detailed process for contributing code, including Git flow, PRs, and commit messages.
│   ├── 020-development-environment-setup.md      # Instructions for setting up local development environments for all services.
│   ├── 030-development-standards.md              # In-depth explanation and application of core development principles (SOLID, DRY, KISS, TDD).
│   ├── 040-code-style-guidelines.md              # Specific rules and configurations for code formatting and linting across languages.
│   ├── 050-security-guidelines.md                # Best practices and rules for secure coding in both backend and frontend.
│   ├── 060-testing-strategy.md                   # Detailed guide on the project's testing methodology and tools (unit, integration, E2E).
│   ├── 070-performance-optimization.md           # Techniques and considerations for optimizing performance across the stack.
│   ├── 080-error-handling-logging.md             # Standardized approach for error handling and logging within the application.
│   └── 090-documentation-standards.md            # Guidelines and best practices for creating and maintaining project documentation.
├── domain-specific/                              # Documentation related to specific electrical engineering domains.
│   ├── 000-electrical-standards.md               # High-level reference and overview of relevant electrical engineering standards (IEEE, IEC, EN).
│   ├── 010-heat-tracing-theory.md                # Theoretical background and principles behind heat tracing calculations.
│   └── 020-electrical-components.md              # Detailed information on electrical components and their properties.
├── server/                                       # Documentation for the Main FastAPI API Server (Primary Gateway).
│   ├── README.md                                 # Overview of the FastAPI API Server's role, responsibilities, and main entry points.
│   ├── src/                                      # Documentation mirroring the `server/src/` directory.
│   │   ├── alembic/                              # Documentation for database migrations and schema evolution.
│   │   │   └── migrations-guide.md
│   │   ├── api/                                  # Documentation for the FastAPI routes and external API endpoints.
│   │   │   ├── routes-overview.md                # General overview of route structure.
│   │   │   ├── authentication.md                 # JWT and other security mechanisms.
│   │   │   ├── users-api.md                      # Documentation for user-related endpoints. (Now covers functionality previously externalized)
│   │   │   └── calculations-api.md               # Documentation for calculation-related endpoints.
│   │   ├── config/                               # Documentation for application configuration, environment variables, etc.
│   │   │   └── app-settings.md
│   │   ├── core/                                 # Documentation for the core business logic and unified patterns.
│   │   │   ├── calculations/                     # In-depth documentation of electrical engineering calculation logic within FastAPI.
│   │   │   │   └── calculation-module-details.md
│   │   │   ├── database/                         # Database configuration and connection management.
│   │   │   │   └── database-setup.md
│   │   │   ├── enums/                            # Definition and usage of domain-specific enumerations.
│   │   │   │   └── domain-enums.md
│   │   │   ├── errors/                           # Unified error handling, custom exception types, and their usage.
│   │   │   │   └── error-handling-guide.md
│   │   │   ├── integrations/                     # Details on how FastAPI integrates with *other C-based services*.
│   │   │   │   ├── internal-service-calls.md     # Guide to making internal gRPC/REST calls to other services.
│   │   │   │   └── cad-integration-patterns.md   # Specific patterns for CAD integration.
│   │   │   ├── models/                           # SQLAlchemy ORM models and their relationships.
│   │   │   │   └── orm-models-guide.md
│   │   │   ├── monitoring/                       # Unified performance monitoring setup and usage.
│   │   │   │   └── performance-monitoring.md
│   │   │   ├── repositories/                     # Data access layer documentation, specific repository interfaces and implementations.
│   │   │   │   └── repository-pattern.md
│   │   │   ├── schemas/                          # Pydantic validation schemas for requests and responses.
│   │   │   │   └── data-validation-schemas.md
│   │   │   ├── security/                         # Unified security validation and authorization mechanisms.
│   │   │   │   └── security-implementation.md
│   │   │   ├── standards/                        # Implementation of engineering standards compliance within FastAPI's core logic.
│   │   │   │   └── standards-validation-logic.md
│   │   │   └── utils/                            # General utility functions and helpers.
│   │   │       └── common-utilities.md
│   │   ├── middleware/                           # Custom middleware implementations and their functions.
│   │   │   └── custom-middleware.md
│   │   ├── app-main-entrypoints.md               # Documentation for `app.py` and `main.py` - application instance and entry point.
│   └── tests/                                    # Documentation for the comprehensive test suite for the FastAPI server.
│       ├── unit-tests-guide.md
│       └── integration-tests-guide.md
└── standards-compliance/                         # Detailed documentation on the project's implementation and verification of compliance with specific international standards.
    ├── iec-60079.md                              # Focus on IEC 60079 series (Explosive atmospheres) compliance.
    ├── iec-61508.md                              # Focus on IEC 61508 series (Functional safety of E/E/PE safety-related systems) compliance.
    └── ieee-standards.md                         # Overview and application of relevant IEEE standards compliance within the project.
├── tool-specific-guides/                         # Guides for specific technologies and tools used in the project.
│   ├── 010-fastapi-guide.md                      # Best practices and advanced usage for FastAPI (general concepts).
│   ├── 020-nextjs-guide.md                       # Specific patterns and optimizations for Next.js.
│   ├── 030-sqlalchemy-guide.md                   # Advanced SQLAlchemy usage, ORM patterns, and migrations.
│   ├── 040-react-query-zustand-guide.md          # Comprehensive guide on state management with React Query and Zustand.
│   ├── 050-tailwind-css-shadcn-ui-guide.md       # Detailed guide on styling with Tailwind CSS and using shadcn/ui components.
│   └── 060-testing-tools-guide.md                # In-depth usage of Vitest, React Testing Library, Playwright, and Pytest.
```

### Maintain and Enforce Knowledge Base Quality

* **Version Control:** All documentation must be under version control (`git`) and follow the same rigorous branching and pull request processes as the code.
* **Regular Review:** Schedule regular reviews and updates of the documentation to reflect code changes, new features, and evolving best practices.
* **Actionable Content:** Ensure the documentation is not just descriptive but prescriptive, providing clear instructions and examples for developers and AI agents.
* **Searchability:** While not explicitly a file structure point, the organized markdown files will naturally support search functionalities for AI agents.
* **Zero-Tolerance Policy:** Extend the "zero tolerance for warnings or technical debt" to documentation, ensuring accuracy, completeness, and adherence to documentation standards.
