# src/core/models/general/project.py
"""Project Database Model.

This module defines the SQLAlchemy model for the Project entity,
including validation and relationships with other entities.
"""

# Define schema for voltage validation
import json
from src.config.logging_config import logger
import datetime
from typing import TYPE_CHECKING, Optional, Any

from pydantic import BaseModel, Field, field_validator
from sqlalchemy import ForeignKey, Text, UniqueConstraint, event
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.errors.exceptions import DataValidationError
# Lazy import to avoid circular dependencies - handle_validation_errors imported at end of file

# Import JSON validation utilities for enhanced model validation
from src.core.models.base import Base, CommonColumns, EnumType, SoftDeleteColumns
from src.core.enums import InstallationEnvironment

# if TYPE_CHECKING:
#     from .cable_installation_circumstance import CableInstallationCircumstance
#     from .cable_selection_matrix import CableSelectionMatrix
#     from .calculations import (
#         BatchCalculationOperation,
#         CalculationResult,
#         StandardsValidationResult,
#     )
#     from .circuit import Circuit
#     from .documents import ExportedDocument, ImportedDataRevision
#     from .electrical import (
#         CableRoute,
#         CableSegment,
#         ElectricalNode,
#         LoadCalculation,
#         VoltageDropCalculation,
#     )
#     from .equipment import Equipment
#     from .heat_tracing import Pipe, Tank
#     from .switchboard import Switchboard


class VoltagesSchema(BaseModel):
    """Schema for validating voltages JSON data."""

    voltages: list[float] = Field(..., description="List of available voltages")

    @field_validator("voltages")
    @classmethod
    def validate_voltages(cls, v: list[float]) -> list[float]:
        """Validate that all voltages are positive numbers."""
        for voltage in v:
            if voltage <= 0:
                raise ValueError("All voltages must be positive numbers")
        return v


class Project(CommonColumns, SoftDeleteColumns, Base):
    """Project model representing a heat tracing design project.

    This model stores project-level information including environmental parameters,
    design specifications, and default settings. It serves as the root entity
    for all project-related components (pipes, tanks, circuits, etc.).

    Attributes:
        project_number: Unique project identifier/code
        description: Optional project description
        designer: Name of the project designer
        min_ambient_temp_c: Minimum ambient temperature in Celsius
        max_ambient_temp_c: Maximum ambient temperature in Celsius
        desired_maintenance_temp_c: Target maintenance temperature in Celsius
        wind_speed_ms: Wind speed in meters per second (optional)
        installation_environment: Indoor/outdoor installation environment
        available_voltages_json: JSON string of available voltages
        default_cable_manufacturer: Default cable manufacturer name
        default_control_device_manufacturer: Default control device manufacturer

    Relationships:
        switchboards: Associated switchboard components
        pipes: Associated pipe components requiring heat tracing
        tanks: Associated tank components requiring heat tracing
        electrical_nodes: Associated electrical connection points
        cable_routes: Associated cable routing information
        Various calculation and validation results

    """

    __tablename__ = "projects"

    project_number: Mapped[str] = mapped_column(
        nullable=False
    )  # REMOVE UniqueConstraint from here
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    designer: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Client and project management fields
    client: Mapped[Optional[str]] = mapped_column(nullable=True)
    location: Mapped[Optional[str]] = mapped_column(nullable=True)
    status: Mapped[str] = mapped_column(nullable=False, default="active")
    created_by: Mapped[Optional[str]] = mapped_column(nullable=True)
    min_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    max_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    desired_maintenance_temp_c: Mapped[float] = mapped_column(nullable=False)
    wind_speed_ms: Mapped[Optional[float]] = mapped_column(nullable=True)
    installation_environment: Mapped[Optional[InstallationEnvironment]] = mapped_column(
        EnumType(InstallationEnvironment), nullable=True
    )
    available_voltages_json: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    default_cable_manufacturer: Mapped[Optional[str]] = mapped_column(nullable=True)
    default_control_device_manufacturer: Mapped[Optional[str]] = mapped_column(
        nullable=True
    )

    # Default cable installation circumstance
    # TODO: Re-enable when cable_installation_circumstances table is created
    # default_cable_circumstance_id: Mapped[int | None] = mapped_column(
    #     ForeignKey("cable_installation_circumstances.id"), nullable=True
    # )

    # # Relationships (using string forward references for models defined in other files)
    # switchboards: Mapped[list["Switchboard"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # pipes: Mapped[list["Pipe"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # tanks: Mapped[list["Tank"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # circuits: Mapped[list["Circuit"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # equipment: Mapped[list["Equipment"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # imported_data_revisions: Mapped[list["ImportedDataRevision"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # exported_documents: Mapped[list["ExportedDocument"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # cable_routes: Mapped[list["CableRoute"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # cable_segments: Mapped[list["CableSegment"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # load_calculations: Mapped[list["LoadCalculation"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # voltage_drop_calculations: Mapped[list["VoltageDropCalculation"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # calculation_results: Mapped[list["CalculationResult"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )
    # standards_validation_results: Mapped[list["StandardsValidationResult"]] = (
    #     relationship(back_populates="project", cascade="all, delete-orphan")
    # )
    # batch_calculation_operations: Mapped[list["BatchCalculationOperation"]] = (
    #     relationship(back_populates="project", cascade="all, delete-orphan")
    # )
    # cable_selection_matrices: Mapped[list["CableSelectionMatrix"]] = relationship(
    #     back_populates="project", cascade="all, delete-orphan"
    # )

    # # Cable installation circumstance relationship
    # default_cable_circumstance: Mapped[Optional["CableInstallationCircumstance"]] = (
    #     relationship(
    #         "CableInstallationCircumstance",
    #         back_populates="projects_as_default",
    #         foreign_keys=[default_cable_circumstance_id],
    #     )
    # )

    __table_args__ = (
        UniqueConstraint("name", name="uq_project_name"),
        UniqueConstraint(
            "project_number", name="uq_project_number"
        ),  # ADD UniqueConstraint here
    )

    def __repr__(self) -> str:
        return f"<Project(id={self.id}, name='{self.name}', project_number='{self.project_number}')>"


def validate_project_data(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before insert/update.

    Args:
        mapper: SQLAlchemy mapper
        connection: Database connection
        target: Project instance being validated

    Raises:
        DataValidationError: If validation fails

    """
    errors = []

    # Validate temperature ranges (only if both values are not None)
    if (
        target.min_ambient_temp_c is not None
        and target.max_ambient_temp_c is not None
        and target.min_ambient_temp_c >= target.max_ambient_temp_c
    ):
        errors.append(
            {
                "loc": ["min_ambient_temp_c", "max_ambient_temp_c"],
                "msg": "Minimum ambient temperature must be less than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate maintenance temperature (only if both values are not None)
    if (
        target.desired_maintenance_temp_c is not None
        and target.max_ambient_temp_c is not None
        and target.desired_maintenance_temp_c <= target.max_ambient_temp_c
    ):
        errors.append(
            {
                "loc": ["desired_maintenance_temp_c"],
                "msg": "Desired maintenance temperature must be greater than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate temperature ranges are reasonable (only if values are not None)
    if target.min_ambient_temp_c is not None and (
        target.min_ambient_temp_c < -100 or target.min_ambient_temp_c > 100
    ):
        errors.append(
            {
                "loc": ["min_ambient_temp_c"],
                "msg": "Minimum ambient temperature must be between -100°C and 100°C",
                "type": "value_error",
            }
        )

    if target.max_ambient_temp_c is not None and (
        target.max_ambient_temp_c < -50 or target.max_ambient_temp_c > 150
    ):
        errors.append(
            {
                "loc": ["max_ambient_temp_c"],
                "msg": "Maximum ambient temperature must be between -50°C and 150°C",
                "type": "value_error",
            }
        )

    if target.desired_maintenance_temp_c is not None and (
        target.desired_maintenance_temp_c < 0 or target.desired_maintenance_temp_c > 500
    ):
        errors.append(
            {
                "loc": ["desired_maintenance_temp_c"],
                "msg": "Desired maintenance temperature must be between 0°C and 500°C",
                "type": "value_error",
            }
        )

    # Validate wind speed
    if target.wind_speed_ms is not None and target.wind_speed_ms < 0:
        errors.append(
            {
                "loc": ["wind_speed_ms"],
                "msg": "Wind speed cannot be negative",
                "type": "value_error",
            }
        )

    # Validate available voltages JSON
    if target.available_voltages_json:
        voltages_data = json.loads(target.available_voltages_json)

        # Handle both array format [120, 240, 480] and object format {"voltages": [120, 240, 480]}
        voltage_list = None
        if isinstance(voltages_data, list):
            # Direct array format: [120, 240, 480]
            voltage_list = voltages_data
        elif isinstance(voltages_data, dict) and "voltages" in voltages_data:
            # Object format: {"voltages": [120, 240, 480]}
            if isinstance(voltages_data["voltages"], list):
                voltage_list = voltages_data["voltages"]
            else:
                errors.append(
                    {
                        "loc": ["available_voltages_json"],
                        "msg": "Voltages must be an array",
                        "type": "value_error",
                    }
                )
        else:
            errors.append(
                {
                    "loc": ["available_voltages_json"],
                    "msg": "Available voltages must be a JSON array or object with 'voltages' key",
                    "type": "value_error",
                }
            )

        # Validate voltage values if we have a valid list
        if voltage_list is not None:
            for voltage in voltage_list:
                if not isinstance(voltage, (int, float)) or voltage <= 0:
                    errors.append(
                        {
                            "loc": ["available_voltages_json"],
                            "msg": "All voltage values must be positive numbers",
                            "type": "value_error",
                        }
                    )
                    break

    # Validate project name and number are not empty
    if not target.name or not target.name.strip():
        errors.append(
            {
                "loc": ["name"],
                "msg": "Project name cannot be empty",
                "type": "value_error",
            }
        )

    if not target.project_number or not target.project_number.strip():
        errors.append(
            {
                "loc": ["project_number"],
                "msg": "Project number cannot be empty",
                "type": "value_error",
            }
        )

    # If there are validation errors, raise exception
    if errors:
        logger.warning(f"Project validation failed: {len(errors)} errors")
        raise DataValidationError(details={"validation_errors": errors})

    logger.debug(f"Project validation passed for: {target.name}")


# Register event listeners for Project model validation
@event.listens_for(Project, "before_insert")
def before_insert_listener(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before insert."""
    validate_project_data(mapper, connection, target)


@event.listens_for(Project, "before_update")
def before_update_listener(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before update."""
    validate_project_data(mapper, connection, target)


# Apply unified error handling to functions after definition to avoid circular imports
def _apply_unified_error_handling_to_project() -> None:
    """Apply unified error handling decorators to project validation functions."""
    try:
        from src.core.errors.unified_error_handler import handle_validation_errors

        # Apply error handling to the validation function
        global validate_project_data
        if not hasattr(validate_project_data, "_unified_error_wrapped"):
            validate_project_data = handle_validation_errors("project_data_validation")(
                validate_project_data
            )
            try:
                validate_project_data._unified_error_wrapped = True  # type: ignore
            except AttributeError:
                # If we can't set the attribute, just continue
                pass

    except ImportError:
        # Unified error handler not available, skip wrapping
        pass


# Apply the error handling
_apply_unified_error_handling_to_project()
