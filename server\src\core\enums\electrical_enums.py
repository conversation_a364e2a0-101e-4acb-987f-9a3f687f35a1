# src/core/enums/electrical_enums.py
"""
This module defines comprehensive enumeration types specific to electrical
engineering design within the Ultimate Electrical Designer application.
It serves as the single source of truth for classifying electrical components,
system elements, circuit characteristics, load types, and design criteria.
The enums herein are critical for accurate data modeling, calculation inputs,
and report generation in electrical design.
"""
from enum import Enum

class ComponentCategoryType(Enum):
    """
    Broad categories for electrical components and equipment.
    This helps in high-level classification and filtering.
    """
    POWER_DISTRIBUTION = "Power Distribution"
    CABLES_AND_WIRING = "Cables & Wiring"
    PROTECTION_DEVICES = "Protection Devices"
    SWITCHING_AND_CONTROL = "Switching & Control"
    MEASUREMENT_AND_MONITORING = "Measurement & Monitoring"
    ENCLOSURES_AND_MOUNTING = "Enclosures & Mounting"
    GROUNDING_AND_BONDING = "Grounding & Bonding"
    POWER_SOURCES = "Power Sources" # Renamed from 'Power Supply' for broader scope
    LOADS = "Loads" # For motors, heaters, lighting etc.
    COMMUNICATION = "Communication"
    SAFETY_AND_EMERGENCY = "Safety & Emergency"
    HEAT_TRACING = "Heat Tracing System" # Moved here as it's a specific electrical application system
    CABLE_MANAGEMENT = "Cable Management"
    OTHER_ELECTRICAL = "Other Electrical" # For anything not fitting existing categories

class ComponentType(Enum):
    """
    Detailed types of electrical components.
    This replaces both previous `ComponentType` and `EquipmentType` for a single source of truth.
    Each physical item, whether large equipment or a small device, is a ComponentType.
    """
    # Power Distribution & Control Equipment
    SWITCHBOARD = "Switchboard"
    MOTOR_CONTROL_CENTER = "Motor Control Center (MCC)"
    DISTRIBUTION_BOARD = "Distribution Board (DB)"
    PANELBOARD = "Panelboard"
    MAIN_SWITCHBOARD = "Main Switchboard (MSB)" # Specific type of switchboard
    SUB_SWITCHBOARD = "Sub Switchboard (SSB)" # Specific type of switchboard
    CONTROL_PANEL = "Control Panel"
    TRANSFORMER = "Transformer"
    GENERATOR = "Generator"
    UPS = "Uninterruptible Power Supply (UPS)"
    BATTERY_BANK = "Battery Bank"
    DC_POWER_SUPPLY = "DC Power Supply"
    SURGE_PROTECTIVE_DEVICE = "Surge Protective Device (SPD)" # Renamed for clarity
    CAPACITOR_BANK = "Capacitor Bank"
    AUTOMATIC_TRANSFER_SWITCH = "Automatic Transfer Switch (ATS)" # Renamed for clarity
    MANUAL_TRANSFER_SWITCH = "Manual Transfer Switch (MTS)" # New
    VARIABLE_FREQUENCY_DRIVE = "Variable Frequency Drive (VFD)"
    SOFT_STARTER = "Soft Starter"
    MOTOR_STARTER = "Motor Starter" # Generic starter, VFD and Soft Starter are specific types

    # Protection Devices
    CIRCUIT_BREAKER = "Circuit Breaker"
    FUSE = "Fuse"
    RESIDUAL_CURRENT_DEVICE = "Residual Current Device (RCD)"
    OVERLOAD_RELAY = "Overload Relay"
    PROTECTIVE_RELAY = "Protective Relay" # More general than just "Relay" if it's for protection

    # Cables & Wiring
    POWER_CABLE = "Power Cable"
    CONTROL_CABLE = "Control Cable"
    INSTRUMENTATION_CABLE = "Instrumentation Cable"
    COMMUNICATION_CABLE = "Communication Cable"
    FIBER_OPTIC_CABLE = "Fiber Optic Cable"
    COAXIAL_CABLE = "Coaxial Cable"
    BUSBAR = "Busbar"
    GROUNDING_CONDUCTOR = "Grounding Conductor" # Specific conductor for grounding
    BONDING_CONDUCTOR = "Bonding Conductor" # Specific conductor for bonding

    # Cable Management
    CABLE_TRAY = "Cable Tray"
    CONDUIT = "Conduit"
    CABLE_DUCT = "Cable Duct" # New
    CABLE_LADDER = "Cable Ladder" # New

    # Switching & Control
    DISCONNECT_SWITCH = "Disconnect Switch"
    LOAD_BREAK_SWITCH = "Load Break Switch" # New, distinct from disconnect
    ISOLATION_SWITCH = "Isolation Switch"
    CONTACTOR = "Contactor"
    CONTROL_RELAY = "Control Relay" # For general control applications (EMR/SSR if needed specific)
    PUSH_BUTTON = "Push Button"
    SELECTOR_SWITCH = "Selector Switch"
    PILOT_LIGHT = "Pilot Light"
    LIMIT_SWITCH = "Limit Switch"
    PROXIMITY_SWITCH = "Proximity Switch"
    TIMER = "Timer"
    COUNTER = "Counter"
    THERMOSTAT = "Thermostat" # General purpose thermal switch/controller
    PRESSURE_SWITCH = "Pressure Switch"
    FLOW_SWITCH = "Flow Switch"
    LEVEL_SWITCH = "Level Switch"

    # Measurement & Monitoring
    AMMETER = "Ammeter"
    VOLTMETER = "Voltmeter"
    POWER_METER = "Power Meter"
    ENERGY_METER = "Energy Meter"
    CURRENT_TRANSFORMER = "Current Transformer (CT)"
    VOLTAGE_TRANSFORMER = "Voltage Transformer (VT/PT)"
    TEMPERATURE_SENSOR = "Temperature Sensor"
    PRESSURE_SENSOR = "Pressure Sensor"
    FLOW_SENSOR = "Flow Sensor"
    LEVEL_SENSOR = "Level Sensor"
    HUMIDITY_SENSOR = "Humidity Sensor" # New
    VIBRATION_SENSOR = "Vibration Sensor" # New
    GAS_DETECTOR = "Gas Detector" # New
    SMOKE_DETECTOR = "Smoke Detector" # New
    MOTION_SENSOR = "Motion Sensor" # New

    # Enclosures & Mounting
    JUNCTION_BOX = "Junction Box"
    TERMINAL_BOX = "Terminal Box" # Can be distinct from junction box
    ENCLOSURE = "Enclosure" # Generic enclosure
    TERMINAL_BLOCK = "Terminal Block"
    CONNECTOR = "Connector" # General electrical connector
    CABLE_GLAND = "Cable Gland" # New
    CABLE_LUG = "Cable Lug" # New

    # Loads
    ELECTRIC_MOTOR = "Electric Motor"
    HEATER = "Heater"
    LIGHTING_FIXTURE = "Lighting Fixture"
    FAN = "Fan"
    PUMP = "Pump"
    COMPRESSOR = "Compressor"
    HVAC_UNIT = "HVAC Unit" # New
    OVEN = "Oven" # New
    FURNACE = "Furnace" # New
    WELDING_MACHINE = "Welding Machine"
    RECTIFIER = "Rectifier" # Can be a load, or part of a power supply
    VALVE_ACTUATOR = "Valve Actuator" # New, specific type of load

    # Heat Tracing Components (specific system components)
    HEAT_TRACING_CABLE = "Heat Tracing Cable"
    HEAT_TRACING_CONTROLLER = "Heat Tracing Controller"
    POWER_DISTRIBUTION_ENCLOSURE_HT = "Heat Tracing Power Distribution Enclosure"
    JUNCTION_BOX_HT = "Heat Tracing Junction Box" # Specific type of junction box for HT

    # Safety & Emergency (distinct from general protection)
    EMERGENCY_STOP_BUTTON = "Emergency Stop Button"
    FIRE_ALARM_CONTROL_PANEL = "Fire Alarm Control Panel"
    FIRE_ALARM_DETECTOR = "Fire Alarm Detector" # Smoke, heat, flame
    STROBE_LIGHT = "Strobe Light" # For alarms
    HORN = "Horn" # For alarms
    EMERGENCY_LIGHTING_FIXTURE = "Emergency Lighting Fixture"

    # Communication Network Components
    ETHERNET_SWITCH = "Ethernet Switch"
    WIRELESS_ACCESS_POINT = "Wireless Access Point"
    FIBER_OPTIC_TRANSCIEVER = "Fiber Optic Transceiver" # New
    INDUSTRIAL_ROUTER = "Industrial Router" # New

    # Other / Miscellaneous
    JUNCTION_TERMINAL = "Junction Terminal" # Can refer to a single terminal point
    MISCELLANEOUS_ELECTRICAL = "Miscellaneous Electrical Component" # Catch-all

# COMPONENT_TYPE_TO_CATEGORY_MAPPING for Data Integrity and Categorization
COMPONENT_TYPE_TO_CATEGORY_MAPPING = {
    # Power Distribution
    ComponentType.SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MOTOR_CONTROL_CENTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.DISTRIBUTION_BOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.PANELBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MAIN_SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.SUB_SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.CONTROL_PANEL: ComponentCategoryType.POWER_DISTRIBUTION, # Can also be control
    ComponentType.TRANSFORMER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.VARIABLE_FREQUENCY_DRIVE: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.SOFT_STARTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MOTOR_STARTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.CAPACITOR_BANK: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.AUTOMATIC_TRANSFER_SWITCH: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MANUAL_TRANSFER_SWITCH: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.BUSBAR: ComponentCategoryType.POWER_DISTRIBUTION,

    # Cables & Wiring
    ComponentType.POWER_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.CONTROL_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.INSTRUMENTATION_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.COMMUNICATION_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.FIBER_OPTIC_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.COAXIAL_CABLE: ComponentCategoryType.CABLES_AND_WIRING,

    # Protection Devices
    ComponentType.CIRCUIT_BREAKER: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.FUSE: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.RESIDUAL_CURRENT_DEVICE: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.OVERLOAD_RELAY: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.PROTECTIVE_RELAY: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.SURGE_PROTECTIVE_DEVICE: ComponentCategoryType.PROTECTION_DEVICES,

    # Switching & Control
    ComponentType.DISCONNECT_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LOAD_BREAK_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.ISOLATION_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.CONTACTOR: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.CONTROL_RELAY: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PUSH_BUTTON: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.SELECTOR_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PILOT_LIGHT: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LIMIT_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PROXIMITY_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.TIMER: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.COUNTER: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.THERMOSTAT: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PRESSURE_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.FLOW_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LEVEL_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.VALVE_ACTUATOR: ComponentCategoryType.SWITCHING_AND_CONTROL, # Often electrically controlled

    # Measurement & Monitoring
    ComponentType.AMMETER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VOLTMETER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.POWER_METER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.ENERGY_METER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.CURRENT_TRANSFORMER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VOLTAGE_TRANSFORMER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.TEMPERATURE_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.PRESSURE_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.FLOW_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.LEVEL_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.HUMIDITY_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VIBRATION_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.GAS_DETECTOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.SMOKE_DETECTOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.MOTION_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,

    # Enclosures & Mounting
    ComponentType.JUNCTION_BOX: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.TERMINAL_BOX: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.ENCLOSURE: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.TERMINAL_BLOCK: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CONNECTOR: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CABLE_GLAND: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CABLE_LUG: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.JUNCTION_TERMINAL: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,

    # Grounding & Bonding
    ComponentType.GROUNDING_CONDUCTOR: ComponentCategoryType.GROUNDING_AND_BONDING,
    ComponentType.BONDING_CONDUCTOR: ComponentCategoryType.GROUNDING_AND_BONDING, # Specific conductor for bonding

    # Power Sources
    ComponentType.GENERATOR: ComponentCategoryType.POWER_SOURCES,
    ComponentType.UPS: ComponentCategoryType.POWER_SOURCES,
    ComponentType.BATTERY_BANK: ComponentCategoryType.POWER_SOURCES,
    ComponentType.DC_POWER_SUPPLY: ComponentCategoryType.POWER_SOURCES,

    # Loads
    ComponentType.ELECTRIC_MOTOR: ComponentCategoryType.LOADS,
    ComponentType.HEATER: ComponentCategoryType.LOADS,
    ComponentType.LIGHTING_FIXTURE: ComponentCategoryType.LOADS,
    ComponentType.FAN: ComponentCategoryType.LOADS,
    ComponentType.PUMP: ComponentCategoryType.LOADS,
    ComponentType.COMPRESSOR: ComponentCategoryType.LOADS,
    ComponentType.HVAC_UNIT: ComponentCategoryType.LOADS,
    ComponentType.OVEN: ComponentCategoryType.LOADS,
    ComponentType.FURNACE: ComponentCategoryType.LOADS,
    ComponentType.WELDING_MACHINE: ComponentCategoryType.LOADS,
    ComponentType.RECTIFIER: ComponentCategoryType.LOADS,


    # Communication
    ComponentType.ETHERNET_SWITCH: ComponentCategoryType.COMMUNICATION,
    ComponentType.WIRELESS_ACCESS_POINT: ComponentCategoryType.COMMUNICATION,
    ComponentType.FIBER_OPTIC_TRANSCIEVER: ComponentCategoryType.COMMUNICATION,
    ComponentType.INDUSTRIAL_ROUTER: ComponentCategoryType.COMMUNICATION,

    # Safety & Emergency
    ComponentType.EMERGENCY_STOP_BUTTON: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.FIRE_ALARM_CONTROL_PANEL: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.FIRE_ALARM_DETECTOR: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.STROBE_LIGHT: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.HORN: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.EMERGENCY_LIGHTING_FIXTURE: ComponentCategoryType.SAFETY_AND_EMERGENCY,

    # Heat Tracing System
    ComponentType.HEAT_TRACING_CABLE: ComponentCategoryType.HEAT_TRACING,
    ComponentType.HEAT_TRACING_CONTROLLER: ComponentCategoryType.HEAT_TRACING,
    ComponentType.POWER_DISTRIBUTION_ENCLOSURE_HT: ComponentCategoryType.HEAT_TRACING,
    ComponentType.JUNCTION_BOX_HT: ComponentCategoryType.HEAT_TRACING,

    # Cable Management
    ComponentType.CABLE_TRAY: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CONDUIT: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CABLE_DUCT: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CABLE_LADDER: ComponentCategoryType.CABLE_MANAGEMENT,

    # Other Electrical
    ComponentType.MISCELLANEOUS_ELECTRICAL: ComponentCategoryType.OTHER_ELECTRICAL,
}

class ComponentFunctionalCategory(Enum):
    """
    Functional categories for components, especially useful for systems engineering.
    This provides an alternative or complementary view to ComponentCategoryType.
    """
    POWER_GENERATION = "Power Generation"
    POWER_TRANSMISSION = "Power Transmission"
    POWER_DISTRIBUTION = "Power Distribution"
    PROCESS_CONTROL = "Process Control"
    SAFETY_INSTRUMENTED_SYSTEM = "Safety Instrumented System (SIS)"
    FIRE_AND_GAS_SYSTEM = "Fire & Gas System (F&G)"
    LIGHTING_SYSTEM = "Lighting System"
    COMMUNICATION_NETWORK = "Communication Network"
    MOTOR_CONTROL = "Motor Control"
    MEASUREMENT = "Measurement"
    ALARMING = "Alarming"
    HVAC_CONTROL = "HVAC Control"
    HEAT_TRACING_SYSTEM = "Heat Tracing System"
    DATA_ACQUISITION = "Data Acquisition"
    REMOTE_MONITORING = "Remote Monitoring"
    OTHER_SYSTEM_FUNCTION = "Other System Function"


class ElectricalNodeType(Enum):
    """
    Types of nodes in an electrical network diagram or schematic.
    """
    BUS = "Bus"
    LOAD_CENTER = "Load Center"
    CONNECTION_POINT = "Connection Point"
    SOURCE = "Source"
    PANEL = "Panel"
    SWITCHGEAR = "Switchgear"
    TRANSFORMER_NODE = "Transformer Node" # Distinct from a transformer component, this is a conceptual node

class CableInstallationMethod(Enum):
    """
    Methods for installing electrical cables.
    """
    CABLE_TRAY = "Cable Tray"
    CONDUIT = "Conduit"
    DIRECT_BURIAL = "Direct Burial"
    DUCT_BANK = "Duct Bank"
    OPEN_AIR = "Open Air"
    CABLE_LADDER = "Cable Ladder" # Consistent with ComponentType
    RACEWAY = "Raceway" # Generic term for enclosed wiring pathways
    CABLE_TROUGH = "Cable Trough" # Similar to tray, often covered
    CLIPS_AND_CLEATS = "Clips and Cleats" # For small runs, surface mounting
    RISER = "Riser" # Vertical cable run
    IN_WALL = "In-Wall" # For building wiring

class CircuitType(Enum):
    """
    Describes the primary function or nature of an electrical circuit.
    This enum defines the circuit's purpose, distinct from the load it serves.
    """
    POWER_DISTRIBUTION = "Power Distribution Circuit"
    MOTOR = "Motor Circuit" # For motor loads
    HEATING = "Heating Circuit" # For general heating loads
    LIGHTING = "Lighting Circuit"
    CONTROL = "Control Circuit"
    INSTRUMENTATION = "Instrumentation Circuit"
    COMMUNICATION = "Communication Circuit"
    SAFETY_INSTRUMENTED = "Safety Instrumented Circuit" # For SIS functions
    HEAT_TRACING = "Heat Tracing Circuit"
    EMERGENCY_POWER = "Emergency Power Circuit"
    ESSENTIAL_POWER = "Essential Power Circuit"
    CRITICAL_POWER = "Critical Power Circuit" # Combined redundancy with "Critical"
    AUXILIARY = "Auxiliary Circuit" # For non-primary functions
    UPS_SUPPLIED = "UPS Supplied Circuit" # Circuit specifically supplied by a UPS
    DC_POWER = "DC Power Circuit"
    TELECOMMUNICATIONS = "Telecommunications Circuit" # More specific than general communication

class LoadType(Enum):
    """
    Classifies electrical loads based on their characteristics.
    """
    MOTOR = "Motor Load"
    HEATING = "Heating Load"
    LIGHTING = "Lighting Load"
    ELECTRONIC = "Electronic Load"
    RESISTIVE = "Resistive Load"
    INDUCTIVE = "Inductive Load"
    CAPACITIVE = "Capacitive Load"
    MIXED = "Mixed Load"
    WELDING = "Welding Load"
    RECTIFIER = "Rectifier Load"
    TRANSFORMER_LOAD = "Transformer Load" # For step-down transformer loads
    UPS_LOAD = "UPS Load" # The load connected to a UPS
    HVAC_LOAD = "HVAC Load" # New specific load type
    COMPRESSOR_LOAD = "Compressor Load" # New specific load type
    PUMP_LOAD = "Pump Load" # New specific load type
    OTHER_LOAD = "Other Load"

class LoadCriticality(Enum):
    """
    Defines the criticality of an electrical load, impacting design and redundancy.
    This replaces `CircuitPriority` to be the single source of truth for criticality.
    A circuit's priority would then be derived from the criticality of the loads it serves.
    """
    CRITICAL = "Critical"       # Life safety, essential process, immediate significant impact
    ESSENTIAL = "Essential"     # Important process, significant operational impact if lost
    IMPORTANT = "Important"     # Desirable but not essential for immediate operation
    NORMAL = "Normal"           # Standard operation, low impact if interrupted
    NON_ESSENTIAL = "Non-Essential" # Convenience, can be shed first

class ElectricalCableType(Enum):
    """
    Types of electrical cables based on their construction and common use.
    """
    POWER = "Power Cable"
    CONTROL = "Control Cable"
    INSTRUMENTATION = "Instrumentation Cable"
    COMMUNICATION = "Communication Cable"
    FIBER_OPTIC = "Fiber Optic Cable"
    COAXIAL = "Coaxial Cable"
    FLEXIBLE = "Flexible Cable" # For portable equipment, temporary connections
    BARE_CONDUCTOR = "Bare Conductor" # E.g., for grounding grids, overhead lines

class ElectricalInsulationType(Enum):
    """
    Common types of electrical insulation materials.
    """
    PVC = "Polyvinyl Chloride (PVC)"
    XLPE = "Cross-Linked Polyethylene (XLPE)"
    EPR = "Ethylene Propylene Rubber (EPR)"
    MI = "Mineral Insulated (MI)" # For MI cables
    TEFLON = "Teflon (PTFE)" # For high temperature/specialty
    SILICONE_RUBBER = "Silicone Rubber"

class ConductorMaterial(Enum):
    """
    Materials used for electrical conductors.
    """
    COPPER = "Copper"
    ALUMINUM = "Aluminum"
    COPPER_CLAD_ALUMINUM = "Copper-Clad Aluminum"

class VoltageLevel(Enum):
    """
    Common classifications of voltage levels in electrical systems.
    """
    LOW_VOLTAGE = "Low Voltage (LV)"
    MEDIUM_VOLTAGE = "Medium Voltage (MV)"
    HIGH_VOLTAGE = "High Voltage (HV)"
    EXTRA_HIGH_VOLTAGE = "Extra High Voltage (EHV)" # For transmission
    ULTRA_HIGH_VOLTAGE = "Ultra High Voltage (UHV)" # For very long transmission

class CableSelectionCriteria(Enum):
    """
    Criteria used for selecting the appropriate cable for an application.
    These are the *factors* to consider, not the calculation types themselves.
    """
    CURRENT_CARRYING_CAPACITY = "Current Carrying Capacity (Ampacity)"
    VOLTAGE_DROP = "Voltage Drop"
    SHORT_CIRCUIT_WITHSTAND = "Short Circuit Withstand"
    TEMPERATURE_RATING = "Temperature Rating"
    INSULATION_TYPE = "Insulation Type"
    CONDUCTOR_MATERIAL = "Conductor Material"
    INSTALLATION_METHOD = "Installation Method"
    ENVIRONMENTAL_CONDITIONS = "Environmental Conditions"
    COST = "Cost"
    LENGTH = "Length"
    MECHANICAL_STRENGTH = "Mechanical Strength"
    FLEXIBILITY = "Flexibility"

class ProtectionDeviceType(Enum):
    """
    More specific types of protection devices.
    """
    MCCB = "Moulded Case Circuit Breaker (MCCB)"
    MCB = "Miniature Circuit Breaker (MCB)"
    ACB = "Air Circuit Breaker (ACB)"
    VCB = "Vacuum Circuit Breaker (VCB)"
    GCB = "Gas Circuit Breaker (GCB)" # SF6
    THERMAL_MAGNETIC_BREAKER = "Thermal-Magnetic Circuit Breaker"
    ELECTRONIC_TRIP_BREAKER = "Electronic Trip Circuit Breaker"
    EARTH_LEAKAGE_BREAKER = "Earth Leakage Circuit Breaker (ELCB)"
    FUSE_LINK = "Fuse Link" # Specific component of a fuse
    HRC_FUSE = "High Rupturing Capacity (HRC) Fuse"
    SEMICONDUCTOR_FUSE = "Semiconductor Fuse" # For protecting sensitive electronics
    TIME_DELAY_FUSE = "Time-Delay Fuse"
    INSTANTANEOUS_TRIP_FUSE = "Instantaneous Trip Fuse"
    RESIDUAL_CURRENT_CIRCUIT_BREAKER_WITH_OVERCURRENT = "RCBO (Residual Current Breaker with Overcurrent protection)" # Common device
    ARC_FAULT_CIRCUIT_INTERRUPTER = "Arc Fault Circuit Interrupter (AFCI)" # Safety device
    GROUND_FAULT_CIRCUIT_INTERRUPTER = "Ground Fault Circuit Interrupter (GFCI)" # Safety device
    SURGE_ARRESTER = "Surge Arrester" # For lightning/overvoltage protection

class SwitchboardFunction(Enum):
    """
    Defines the primary function of a switchboard or panel.
    """
    MAIN_DISTRIBUTION = "Main Distribution"
    SUB_DISTRIBUTION = "Sub Distribution"
    MOTOR_CONTROL = "Motor Control"
    LIGHTING_CONTROL = "Lighting Control"
    POWER_FACTOR_CORRECTION = "Power Factor Correction"
    CRITICAL_POWER_DISTRIBUTION = "Critical Power Distribution"
    ESSENTIAL_POWER_DISTRIBUTION = "Essential Power Distribution"
    GENERAL_POWER = "General Power"
    INSTRUMENTATION_DISTRIBUTION = "Instrumentation Distribution"
    CONTROL_SYSTEM = "Control System Panel"
    BUILDING_MANAGEMENT_SYSTEM = "Building Management System (BMS) Panel"

class FeederType(Enum):
    """
    Types of electrical feeders based on their hierarchy and purpose.
    """
    MAIN_FEEDER = "Main Feeder"
    SUB_FEEDER = "Sub Feeder"
    BRANCH_CIRCUIT = "Branch Circuit"
    CRITICAL_FEEDER = "Critical Feeder"
    EMERGENCY_FEEDER = "Emergency Feeder"
    STANDBY_FEEDER = "Standby Feeder"
    MOTOR_FEEDER = "Motor Feeder"
    HVAC_FEEDER = "HVAC Feeder"
    LIGHTING_FEEDER = "Lighting Feeder"
    UTILITY_SUPPLY = "Utility Supply Feeder" # The incoming supply
    DISTRIBUTION_FEEDER = "Distribution Feeder" # General distribution
    CONTROL_FEEDER = "Control Feeder" # For control power
    INSTRUMENTATION_FEEDER = "Instrumentation Feeder" # For instrumentation power