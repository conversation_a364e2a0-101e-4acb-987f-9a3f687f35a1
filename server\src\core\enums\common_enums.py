# src/core/enums/common_enums.py
"""
This module defines common, non-domain-specific enumeration types
that are broadly applicable across various aspects of an engineering project.
These enums provide fundamental classifications for environments, units of measure,
and other universal concepts, ensuring consistency and reusability throughout
the Ultimate Electrical Designer application.
"""
from enum import Enum

class InstallationEnvironment(Enum):
    """
    Defines the typical environments in which electrical and mechanical components are installed.
    """
    INDOOR = "Indoor"
    OUTDOOR = "Outdoor"
    HAZARDOUS_AREA = "Hazardous Area" # Added for completeness in professional design
    UNDERGROUND = "Underground"
    SUBMERGED = "Submerged" # For pumps, cables etc.

class UnitOfMeasure(Enum):
    """
    Standard units of measure for various properties.
    This enum provides common units and can be expanded as needed.
    """
    # Electrical Units
    VOLT = "V"
    AMPERE = "A"
    OHM = "Ω"
    WATT = "W"
    KILOWATT = "kW"
    MEGAWATT = "MW"
    VOLT_AMPERE = "VA"
    KILOVOLT_AMPERE = "kVA"
    MEGAVOLT_AMPERE = "MVA"
    VAR = "VAR"
    KILOVAR = "kVAR"
    HERTZ = "Hz"
    FARAD = "F"
    HENRY = "H"
    JOULE = "J"
    KILOWATT_HOUR = "kWh"

    # Thermal Units
    CELSIUS = "°C"
    FAHRENHEIT = "°F"
    KELVIN = "K"
    WATT_PER_METER = "W/m" # For heat tracing
    BTU_PER_HOUR = "BTU/hr" # For heat loss

    # Length Units
    METER = "m"
    CENTIMETER = "cm"
    MILLIMETER = "mm"
    KILOMETER = "km"
    FOOT = "ft"
    INCH = "in"
    MILE = "mi"

    # Mass Units
    KILOGRAM = "kg"
    GRAM = "g"
    POUND = "lb"
    TONNE = "tonne"

    # Time Units
    SECOND = "s"
    MINUTE = "min"
    HOUR = "hr"
    DAY = "day"

    # Pressure Units
    PASCAL = "Pa"
    KILOPASCAL = "kPa"
    MEGAPASCAL = "MPa"
    PSI = "psi"
    BAR = "bar"

    # Flow Units
    CUBIC_METER_PER_SECOND = "m³/s"
    LITER_PER_SECOND = "L/s"
    GALLON_PER_MINUTE = "gpm"

    # Area Units
    SQUARE_METER = "m²"
    SQUARE_FOOT = "ft²"

    # Volume Units
    CUBIC_METER = "m³"
    LITER = "L"
    GALLON = "gal"

    # Other
    PERCENT = "%"
    NONE = "None" # For cases where no specific unit applies or is measured