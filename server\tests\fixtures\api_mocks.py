from unittest.mock import Mock
from fastapi import Request, Response
import pytest

@pytest.fixture
def mock_app():
    """Create a mock ASGI app for testing."""
    return Mock()

@pytest.fixture
def mock_request():
    """Create a mock request object with proper attributes."""
    request = Mock(spec=Request)
    request.method = "GET"
    request.url = Mock()
    request.url.path = "/api/v1/test"
    request.url.query = ""
    request.query_params = {}
    request.headers = {}
    request.client.host = "127.0.0.1"
    return request

@pytest.fixture
def mock_response():
    """Create a mock response object."""
    response = Mock(spec=Response)
    response.status_code = 200
    response.headers = {}
    response.body = b'{"result": "success"}'
    return response

@pytest.fixture
def mock_call_next(mock_response):
    """Create a mock call_next function that returns a response."""

    async def call_next(request):
        return mock_response

    return call_next