# src/core/enums/heat_tracing_enums.py
"""
This module defines enumeration types specifically tailored for heat tracing
system design within the Ultimate Electrical Designer application.
It covers classifications for heating methods, circuit applications,
sensor types, and heat tracing cable categories. These enums ensure precise
modeling and calculation of heat tracing requirements for pipelines and equipment.
"""
from enum import Enum

# Inherits from ElectricalCableType for general cable classification
from .electrical_enums import ElectricalCableType, ComponentType

class HeatingMethodType(Enum):
    """
    Methods used for applying heat tracing cable to a pipe or equipment.
    """
    SINGLE_TRACE = "Single Trace" # Formerly "Parallel"
    SPIRAL = "Spiral Trace"
    DOUBLE_TRACE = "Double Trace" # Two cables side-by-side
    MULTIPLE_TRACE = "Multiple Trace" # More than two
    PAD_HEATER = "Pad Heater" # For tanks/vessels
    PANEL_HEATER = "Panel Heater" # For specific surface areas
    GRID_HEATER = "Grid Heater" # For large flat surfaces, floor heating etc.
    TANK_HEATER = "Tank Heater" # Specific for tanks, could be pad, immersion etc.

class HTCircuitApplicationType(Enum):
    """
    Primary application or purpose of a heat tracing circuit.
    """
    FREEZE_PROTECTION = "Freeze Protection"
    PROCESS_TEMPERATURE_MAINTENANCE = "Process Temperature Maintenance"
    ANTI_CONDENSATION = "Anti-Condensation"
    SNOW_MELTING = "Snow Melting"
    HOT_WATER_MAINTENANCE = "Hot Water Maintenance"

class HTSensorType(Enum):
    """
    Types of sensors commonly used in heat tracing systems.
    """
    RTD = "Resistance Temperature Detector (RTD)"
    THERMOCOUPLE = "Thermocouple"
    AMBIENT_TEMPERATURE_SENSOR = "Ambient Temperature Sensor"
    LINE_TEMPERATURE_SENSOR = "Line Temperature Sensor" # Sensor on the pipe/equipment being traced
    PIPE_WALL_SENSOR = "Pipe Wall Sensor" # Specific for measuring pipe wall temp
    THERMISTOR = "Thermistor" # Another type of temp sensor

class HeatTracingCableCategory(Enum):
    """
    Broad categories of heat tracing cables based on their core technology.
    This replaces `HeatTracingCableType` for better categorization,
    and `ComponentType.HEAT_TRACING_CABLE` should be the specific component.
    """
    SELF_REGULATING = "Self-Regulating Cable"
    CONSTANT_WATTAGE = "Constant Wattage Cable"
    MINERAL_INSULATED = "Mineral Insulated (MI) Cable"
    SKIN_EFFECT = "Skin Effect Current Tracing (SECT)" # System, not just a cable type but relevant
    SERIES_RESISTANCE = "Series Resistance Cable" # Less common now, but historically relevant