# src/core/services/dependencies.py
"""Service Dependencies.

This module provides service dependency injection providers for FastAPI.
Services receive their required repositories through dependency injection,
following the layered architecture pattern.
"""

from typing import TYPE_CHECKING
from fastapi import Depends
from sqlalchemy.orm import Session

from src.core.database.dependencies import get_db
from src.core.repositories.repository_dependencies import (
    get_project_repository,
    get_user_repository,
)

if TYPE_CHECKING:
    from src.core.services.general.project_service import ProjectService
    from src.core.services.general.user_service import UserService


def get_project_service(project_repo=Depends(get_project_repository)) -> "ProjectService":
    """Dependency provider for ProjectService.

    Args:
        project_repo: ProjectRepository from dependency injection

    Returns:
        ProjectService: Configured service instance

    """
    from src.core.services.general.project_service import ProjectService

    return ProjectService(project_repo)


def get_user_service(db: Session = Depends(get_db)) -> "UserService":
    """Dependency provider for UserService.

    Args:
        db: Database session from dependency injection

    Returns:
        UserService: Configured service instance

    """
    from src.core.services.general.user_service import UserService

    return UserService(db)
