# src/app.py
"""Application Module for Ultimate Electrical Designer.

This module contains the FastAPI application instance and configuration for the Ultimate Electrical Designer server.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse

# Import main API router
from src.api.main_router import api_router

# Import core modules
from src.middleware.security_middleware import SecurityMiddleware
from src.config.logging_config import logger
from src.config.settings import settings
from src.core.database import close_engine, initialize_database
from src.middleware.caching_middleware import CachingMiddleware
from src.middleware.context_middleware import ContextMiddleware

# Import middleware
from src.middleware.logging_middleware import LoggingMiddleware
from src.middleware.rate_limiting_middleware import RateLimitingMiddleware

# Import unified systems
from src.core.security.enhanced_dependencies import get_unified_security_validator
from src.core.security.unified_security_validator import UnifiedSecurityValidator
from src.core.errors.unified_error_handler import unified_error_handler
from src.core.enums import ErrorContext


# --- Application Lifespan Events ---
@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Handles startup and shutdown events for the application with robust error handling."""
    logger.info("Application startup initiated.")

    try:
        # Initialize database with migrations and fallback handling
        logger.info("Initializing database...")
        engine = initialize_database(run_migrations=True, create_tables_if_needed=True)
        logger.info(f"Database initialized successfully using: {engine.url}")

        # Store engine in app state for access in other parts of the application
        app.state.db_engine = engine

        # Initialize performance optimizer cache
        from src.core.utils.performance_optimizer import performance_optimizer

        app.state.performance_optimizer = performance_optimizer
        logger.info("Performance optimizer initialized with caching support.")

        # Initialize unified security validator
        unified_security_validator = get_unified_security_validator()
        app.state.unified_security_validator = unified_security_validator
        logger.info("Unified security validator initialized successfully.")

        logger.info("Application startup completed successfully.")
        yield  # Application runs

    except Exception as e:
        logger.critical(f"Application startup failed: {e}", exc_info=True)
        # In production, we might want to exit gracefully
        # For now, re-raise to prevent the app from starting with a broken state
        raise

    finally:
        logger.info("Application shutdown initiated.")

        try:
            # Clean up database connections
            close_engine()
            logger.info("Database connections closed.")

            # Clean up performance optimizer cache
            if (
                hasattr(app.state, "performance_optimizer")
                and app.state.performance_optimizer
            ):
                app.state.performance_optimizer.clear_cache()
                logger.info("Performance optimizer cache cleared.")

        except Exception as e:
            logger.error(f"Error during shutdown cleanup: {e}", exc_info=True)

        logger.info("Application shutdown completed.")


# --- FastAPI Application Instance ---
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,  # Attach lifespan context manager
)

# --- Register Middleware ---
# Middleware runs in the order they are added (bottom-up execution on response)
# Order is crucial! Error handling typically goes near the top (bottom of chain)
# so it can catch exceptions from other middleware.

# 1. Context middleware - should be first to set up request context
app.add_middleware(
    ContextMiddleware,
    enable_request_id=True,
    enable_user_context=True,
    enable_locale_detection=True,
    enable_timing=True,
    default_locale="en",
)

# 2. Logging middleware - logs requests/responses with context
app.add_middleware(
    LoggingMiddleware,
    enable_request_logging=True,
    enable_response_logging=True,
    enable_error_logging=True,
    enable_performance_logging=True,
    log_request_body=settings.DEBUG,  # Only log bodies in debug mode
    log_response_body=False,  # Generally don't log response bodies
    exclude_health_checks=True,
)

# 3. Caching middleware - caches responses before security processing
app.add_middleware(
    CachingMiddleware,
    default_ttl=300,  # 5 minutes
    enable_caching=True,
    enable_etag=True,
    enable_user_specific_caching=True,
)

# 4. Rate limiting middleware - advanced rate limiting
app.add_middleware(
    RateLimitingMiddleware,
    default_requests_per_minute=settings.RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE,
    default_burst_size=10,
    enable_per_ip_limiting=settings.RATE_LIMIT_ENABLED,
    enable_per_user_limiting=True,
    enable_endpoint_specific_limits=True,
)

# 5. Security middleware - enhanced with unified security validation
app.add_middleware(
    SecurityMiddleware,
    max_payload_size=10 * 1024 * 1024,  # 10MB
    max_json_depth=100,
    rate_limit_requests=100,  # Basic rate limiting (advanced handled by RateLimitingMiddleware)
    rate_limit_window=60,  # seconds
    enable_xss_protection=True,
    enable_unicode_validation=True,
    jwt_secret_key=settings.SECRET_KEY,
    jwt_algorithm="HS256",
    enable_csrf_protection=True,
    use_unified_security=True,  # Enable unified security integration
)


# Enhanced global exception handler using unified error handling system
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler that uses the unified error handling system.

    Uses the unified error handling system for consistent error handling
    across all layers with comprehensive security context.
    """
    additional_context = {
        "path": request.url.path,
        "method": request.method,
        "user_agent": request.headers.get("user-agent"),
        "client_ip": request.client.host if request.client else None,
    }

    # Use the unified error handler to process the exception
    result = unified_error_handler.handle_exception(
        exc, ErrorContext.API, request, additional_context
    )

    # Return the JSON response
    return result.to_json_response()


# --- Include API Routers ---
app.include_router(
    api_router, prefix="/api"
)  # All API routes will be prefixed with /api


# Example root endpoint
@app.get("/")
async def read_root():
    """Root endpoint providing API welcome message and version information.

    Returns:
        dict: Welcome message with application name and version.
    """
    return {"message": f"Welcome to {settings.APP_NAME} API v{settings.APP_VERSION}"}
