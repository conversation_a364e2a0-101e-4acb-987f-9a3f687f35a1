import uuid
from fastapi.testclient import TestClient
import pytest

from src.core.services.general.user_service import UserService
from src.core.enums.project_management_enums import UserRole
from src.core.schemas.general.user_schemas import UserCreateSchema
from tests.conftest import db_session

#--------------------------------Admin User Fixtures--------------------------------#

unique_suffix = str(uuid.uuid4())[:8]

@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    return {
        "name": f"Test User {unique_suffix}",
        "email": f"testuser.{unique_suffix}@example.com",
        "password_hash": f"hashed_password_{unique_suffix}",
        "role": UserRole.ADMIN,
        "is_active": True,
    }

@pytest.fixture
def test_admin_user(db_session, admin_user_data):
    """Create an admin user in the database."""
    
    user_service = UserService(db_session)
    
    user_create = UserCreateSchema(**admin_user_data)
    user = user_service.create_user(user_create)
    return user

#--------------------------------Regular User Fixtures--------------------------------#

@pytest.fixture
def test_user_data():
    """Create test user data."""
    return {
        "name": f"Test User {unique_suffix}",
        "email": f"testuser.{unique_suffix}@example.com",
        "password_hash": f"hashed_password_{unique_suffix}",
        "role": UserRole.VIEWER,
        "is_active": True,
    }

@pytest.fixture
def test_user(db_session, test_user_data):
    """Create a test user in the database."""

    # UserService expects a database session, not a repository
    user_service = UserService(db_session)

    user_create = UserCreateSchema(**test_user_data)
    user = user_service.create_user(user_create)
    return user

#--------------------------------Inactive User Fixtures--------------------------------#

@pytest.fixture
def test_inactive_user_data():
    """Create test user data."""
    return {
        "name": f"Test User {unique_suffix}",
        "email": f"testuser.{unique_suffix}@example.com",
        "password_hash": f"hashed_password_{unique_suffix}",
        "role": UserRole.VIEWER,
        "is_active": False
    }

@pytest.fixture
def test_inactive_user(db_session, test_inactive_user_data):
    """Create a test inactive user in the database."""

    # UserService expects a database session, not a repository
    user_service = UserService(db_session)

    user_create = UserCreateSchema(**test_inactive_user_data)
    user = user_service.create_user(user_create)
    return user

#--------------------------------Authentication Token Fixtures--------------------------------#

@pytest.fixture
def admin_token(client: TestClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": test_admin_user.hashed_password,
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    return response.json()["access_token"]

@pytest.fixture
def user_token(client: TestClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": test_user.hashed_password,
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    return response.json()["access_token"]

