function Get-FolderStructure {
    param (
        [string]$Path = (Get-Location).Path,
        [array]$ExcludeFolders = @(),
        [array]$ExcludeFileNames = @(),
        [array]$ExcludeFileExtensions = @()
    )

    $root = @{
        Name = (Get-Item $Path).Name
        Path = (Get-Item $Path).FullName
        Type = "Folder"
        Children = @()
    }

    $stack = New-Object System.Collections.Generic.Stack[psobject]
    $stack.Push($root)

    while ($stack.Count -gt 0) {
        $currentItem = $stack.Pop()
        $currentPath = $currentItem.Path

        # Get subdirectories
        Get-ChildItem -LiteralPath $currentPath -Directory -ErrorAction SilentlyContinue | ForEach-Object {
            # Check if the folder should be ignored
            if ($ExcludeFolders -notcontains $_.Name) {
                $folder = @{
                    Name = $_.Name
                    Path = $_.FullName
                    Type = "Folder"
                    Children = @()
                }
                $currentItem.Children += $folder
                $stack.Push($folder)
            }
        }

        # Get files
        Get-ChildItem -LiteralPath $currentPath -File -ErrorAction SilentlyContinue | ForEach-Object {
            $file = @{
                Name = $_.Name
                Path = $_.FullName
                Type = "File"
                Extension = $_.Extension
                SizeKB = [math]::Round($_.Length / 1KB, 2)
            }

            # Check if the file should be ignored
            if ($ExcludeFileNames -notcontains $file.Name -and $ExcludeFileExtensions -notcontains $file.Extension) {
                $currentItem.Children += $file
            }
        }
    }

    return $root
}

# Define the root path (current directory where the script is run)
$rootPath = (Get-Location).Path
$outputFileName = "folder-structure.json"
$outputPath = Join-Path -Path $rootPath -ChildPath $outputFileName

# Define the list of folders to ignore (e.g., .git, .history)
$foldersToIgnore = @(
    ".github",
    ".benchmarks",
    ".history",
    ".vscode",
    ".venv",
    ".pytest_cache",
    ".ruff_cache",
    "node_modules",
    "htmlcov",
    "versions",
    "bin",
    "logs",
    "tests",
    "migrations",
    "obj",
    "dist",
    "build",
    ".git",
    "target",
    "vendor",
    "test_logs",
    "test_reports",
    "__pycache__",
    "$RECYCLE.BIN", # System folder, usually hidden
    "System Volume Information" # System folder, usually hidden
)

# Define the list of exact file names to ignore (e.g., .env, .gitignore)
# These are matched against the *full file name*
$fileNamesToIgnore = @(
    "__init__.py",
    ".env",
    ".gitignore",
    ".gitattributes",
    "Thumbs.db",
    "desktop.ini",
    "config.json.example",
    ".coverage",
    "poetry.toml",
    "poetry.lock"
)

# Define the list of file extensions to ignore (e.g., .pyc, .tmp)
# These are matched against the file's .Extension property
$fileExtensionsToIgnore = @(
    ".pyc",
    ".tmp",
    ".log",
    ".bak",
    ".swp",
    # ".md",
    ".json",
    ".ps1"
)

# Run the script from the current directory, passing all exclude lists
$folderStructure = Get-FolderStructure `
    -Path $rootPath `
    -ExcludeFolders $foldersToIgnore `
    -ExcludeFileNames $fileNamesToIgnore `
    -ExcludeFileExtensions $fileExtensionsToIgnore

# Convert to JSON with a suitable depth and export to a file
# -Depth 0 ensures full depth serialization in modern PowerShell versions (PS 5.1+)
$folderStructure | ConvertTo-Json -Depth 20 | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host "Folder structure exported to: $outputPath"