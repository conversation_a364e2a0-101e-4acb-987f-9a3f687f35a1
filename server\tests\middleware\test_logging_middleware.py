# tests/middleware/test_logging_middleware.py
"""
Comprehensive tests for LoggingMiddleware following robust test patterns.

Tests cover:
- Request/response logging with structured data
- Performance metrics collection
- Error logging with context
- Client information tracking
- Configurable log levels and filtering
- Body logging controls
- Path exclusion functionality
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import Request, Response

from src.middleware.logging_middleware import LoggingMiddleware
from tests.fixtures.middleware_fixtures import logging_middleware
from tests.fixtures.api_mocks import mock_app, mock_request, mock_call_next
from tests.fixtures.app_fixtures import test_app_with_logging

pytestmark = [pytest.mark.integration]


class TestLoggingMiddleware:
    """Comprehensive test suite for LoggingMiddleware."""

    def test_middleware_initialization_default_config(self, mock_app):
        """Test middleware initialization with default configuration."""
        middleware = LoggingMiddleware(mock_app)

        assert middleware.enable_request_logging is True
        assert middleware.enable_response_logging is True
        assert middleware.enable_error_logging is True
        assert middleware.enable_performance_logging is True
        assert middleware.log_request_body is False
        assert middleware.log_response_body is False
        assert middleware.max_body_size == 1024
        assert middleware.exclude_health_checks is True

    def test_middleware_initialization_custom_config(self, mock_app):
        """Test middleware initialization with custom configuration."""
        custom_exclude_paths = {"/custom", "/test"}
        middleware = LoggingMiddleware(
            app=mock_app,
            enable_request_logging=False,
            enable_response_logging=False,
            enable_error_logging=False,
            enable_performance_logging=False,
            log_request_body=True,
            log_response_body=True,
            max_body_size=2048,
            exclude_paths=custom_exclude_paths,
            exclude_health_checks=False,
        )

        assert middleware.enable_request_logging is False
        assert middleware.enable_response_logging is False
        assert middleware.enable_error_logging is False
        assert middleware.enable_performance_logging is False
        assert middleware.log_request_body is True
        assert middleware.log_response_body is True
        assert middleware.max_body_size == 2048
        assert custom_exclude_paths.issubset(middleware.exclude_paths)
        assert middleware.exclude_health_checks is False

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.get_request_id")
    @patch("src.middleware.logging_middleware.get_user_context")
    @patch("src.middleware.logging_middleware.logger")
    async def test_request_logging(
        self,
        mock_logger,
        mock_get_user_context,
        mock_get_request_id,
        logging_middleware,
        mock_request,
        mock_call_next,
    ):
        """Test request logging functionality."""
        mock_get_request_id.return_value = "test-request-id"
        mock_get_user_context.return_value = {"id": "user123"}

        response = await logging_middleware.dispatch(mock_request, mock_call_next)

        # Verify request logging was called
        mock_logger.info.assert_called()

        # Check that logging was called with request information
        # Find the request log call (should contain request information)
        request_log_call = None
        for call in mock_logger.info.call_args_list:
            if (
                len(call) > 1
                and "extra" in call[1]
                and "request_data" in call[1]["extra"]
            ):
                request_log_call = call
                break

        assert request_log_call is not None, "Request logging should have occurred"
        request_data = request_log_call[1]["extra"]["request_data"]
        assert request_data["request_id"] == "test-request-id"
        assert request_data["method"] == "GET"
        assert request_data["path"] == "/api/v1/test"
        assert request_data["client_ip"] == "127.0.0.1"
        assert request_data["user_context"] == {"id": "user123"}

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.get_request_id")
    @patch("src.middleware.logging_middleware.logger")
    async def test_response_logging_success(
        self,
        mock_logger,
        mock_get_request_id,
        logging_middleware,
        mock_request,
        mock_call_next,
    ):
        """Test response logging for successful requests."""
        mock_get_request_id.return_value = "test-request-id"

        response = await logging_middleware.dispatch(mock_request, mock_call_next)

        # Verify response logging was called (should be called twice: request + response)
        assert mock_logger.info.call_count >= 2

        # Find the response log call
        response_log_call = None
        for call in mock_logger.info.call_args_list:
            if "Request completed" in call[0][0]:
                response_log_call = call
                break

        assert response_log_call is not None
        response_data = response_log_call[1]["extra"]["response_data"]
        assert response_data["request_id"] == "test-request-id"
        assert response_data["status_code"] == 200
        assert "duration_ms" in response_data

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.get_request_id")
    @patch("src.middleware.logging_middleware.logger")
    async def test_response_logging_error_status(
        self, mock_logger, mock_get_request_id, logging_middleware, mock_request
    ):
        """Test response logging for error status codes."""
        mock_get_request_id.return_value = "test-request-id"

        # Create call_next that returns error response
        async def error_call_next(request):
            response = Mock(spec=Response)
            response.status_code = 500
            response.headers = {}
            return response

        response = await logging_middleware.dispatch(mock_request, error_call_next)

        # Verify error-level logging was used for 500 status
        mock_logger.error.assert_called()

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.get_request_id")
    @patch("src.middleware.logging_middleware.get_user_context")
    @patch("src.middleware.logging_middleware.logger")
    async def test_error_logging(
        self,
        mock_logger,
        mock_get_user_context,
        mock_get_request_id,
        logging_middleware,
        mock_request,
    ):
        """Test error logging when exception occurs."""
        mock_get_request_id.return_value = "test-request-id"
        mock_get_user_context.return_value = {"id": "user123"}

        # Create call_next that raises exception
        async def failing_call_next(request):
            raise ValueError("Test error")

        with pytest.raises(ValueError):
            await logging_middleware.dispatch(mock_request, failing_call_next)

        # Verify error logging was called
        mock_logger.error.assert_called()

        # Check error log structure
        error_call = mock_logger.error.call_args
        assert "Request failed" in error_call[0][0]
        assert "ValueError" in error_call[0][0]

        error_data = error_call[1]["extra"]["error_data"]
        assert error_data["request_id"] == "test-request-id"
        assert error_data["error_type"] == "ValueError"
        assert error_data["error_message"] == "Test error"
        assert error_data["user_context"] == {"id": "user123"}

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.get_request_id")
    @patch("src.middleware.logging_middleware.logger")
    async def test_performance_logging_slow_request(
        self, mock_logger, mock_get_request_id, logging_middleware, mock_request
    ):
        """Test performance logging for slow requests."""
        mock_get_request_id.return_value = "test-request-id"

        # Create call_next that simulates slow response
        async def slow_call_next(request):
            # Simulate async delay without using AsyncMock incorrectly
            import asyncio

            await asyncio.sleep(0.001)  # Small delay for testing
            response = Mock(spec=Response)
            response.status_code = 200
            response.headers = {}
            return response

        # Mock time to simulate slow request - provide enough values for all calls
        with patch("src.middleware.logging_middleware.time") as mock_time:
            # The middleware calls time.time() multiple times:
            # 1. Start time capture
            # 2. Performance metrics calculation
            # 3. Potentially in error handling if something goes wrong
            mock_time.time.side_effect = [0.0, 3.0, 3.0, 3.0]  # Provide extra values

            response = await logging_middleware.dispatch(mock_request, slow_call_next)

        # Verify slow request warning was logged
        warning_calls = [
            call
            for call in mock_logger.warning.call_args_list
            if "Slow request detected" in call[0][0]
        ]
        assert len(warning_calls) > 0

    def test_should_exclude_path_health_checks(self, logging_middleware):
        """Test path exclusion for health check endpoints."""
        assert logging_middleware._should_exclude_path("/health") is True
        assert logging_middleware._should_exclude_path("/ping") is True
        assert logging_middleware._should_exclude_path("/status") is True
        assert logging_middleware._should_exclude_path("/api/v1/health") is True

    def test_should_exclude_path_configured_paths(self, mock_app):
        """Test path exclusion for configured paths."""
        middleware = LoggingMiddleware(mock_app, exclude_paths={"/custom", "/test"})

        assert middleware._should_exclude_path("/custom") is True
        assert middleware._should_exclude_path("/test") is True
        assert middleware._should_exclude_path("/api/v1/normal") is False

    def test_get_client_ip_forwarded_headers(self, logging_middleware, mock_request):
        """Test client IP extraction from forwarded headers."""
        # Test X-Forwarded-For header
        mock_request.headers = {"x-forwarded-for": "***********, ********"}
        ip = logging_middleware._get_client_ip(mock_request)
        assert ip == "***********"

        # Test X-Real-IP header
        mock_request.headers = {"x-real-ip": "***********"}
        ip = logging_middleware._get_client_ip(mock_request)
        assert ip == "***********"

    def test_get_client_ip_direct_client(self, logging_middleware, mock_request):
        """Test client IP extraction from direct client."""
        mock_request.headers = {}
        mock_request.client.host = "127.0.0.1"

        ip = logging_middleware._get_client_ip(mock_request)
        assert ip == "127.0.0.1"

    def test_get_client_ip_unknown(self, logging_middleware, mock_request):
        """Test client IP extraction when no source available."""
        mock_request.headers = {}
        mock_request.client = None

        ip = logging_middleware._get_client_ip(mock_request)
        assert ip == "unknown"

    def test_should_log_body_content_types(self, logging_middleware, mock_request):
        """Test body logging decision based on content type."""
        # Test JSON content type
        mock_request.headers = {"content-type": "application/json"}
        assert logging_middleware._should_log_body(mock_request) is True

        # Test XML content type
        mock_request.headers = {"content-type": "application/xml"}
        assert logging_middleware._should_log_body(mock_request) is True

        # Test text content type
        mock_request.headers = {"content-type": "text/plain"}
        assert logging_middleware._should_log_body(mock_request) is True

        # Test form data
        mock_request.headers = {"content-type": "application/x-www-form-urlencoded"}
        assert logging_middleware._should_log_body(mock_request) is True

        # Test binary content type (should not log)
        mock_request.headers = {"content-type": "application/octet-stream"}
        assert logging_middleware._should_log_body(mock_request) is False

    @pytest.mark.asyncio
    async def test_excluded_path_bypass(
        self, logging_middleware, mock_request, mock_call_next
    ):
        """Test that excluded paths bypass logging."""
        mock_request.url.path = "/health"

        with patch("src.middleware.logging_middleware.logger") as mock_logger:
            response = await logging_middleware.dispatch(mock_request, mock_call_next)

            # Verify no logging occurred
            mock_logger.info.assert_not_called()
            mock_logger.error.assert_not_called()
            mock_logger.warning.assert_not_called()

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.logger")
    async def test_disabled_logging_features(
        self, mock_logger, mock_app, mock_request, mock_call_next
    ):
        """Test middleware behavior when logging features are disabled."""
        middleware = LoggingMiddleware(
            app=mock_app,
            enable_request_logging=False,
            enable_response_logging=False,
            enable_error_logging=False,
            enable_performance_logging=False,
        )

        response = await middleware.dispatch(mock_request, mock_call_next)

        # Verify no logging occurred
        mock_logger.info.assert_not_called()
        mock_logger.error.assert_not_called()
        mock_logger.warning.assert_not_called()
        mock_logger.debug.assert_not_called()

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.logger")
    async def test_body_logging_enabled(
        self, mock_logger, mock_app, mock_request, mock_call_next
    ):
        """Test request body logging when enabled."""
        middleware = LoggingMiddleware(
            app=mock_app, log_request_body=True, max_body_size=1024
        )

        # Mock request body
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')
        mock_request.headers = {"content-type": "application/json"}

        response = await middleware.dispatch(mock_request, mock_call_next)

        # Verify request logging included body
        mock_logger.info.assert_called()
        call_args = mock_logger.info.call_args
        request_data = call_args[1]["extra"]["request_data"]
        assert "body" in request_data
        assert request_data["body"] == {"test": "data"}

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.logger")
    async def test_body_logging_size_limit(
        self, mock_logger, mock_app, mock_request, mock_call_next
    ):
        """Test request body logging respects size limits."""
        middleware = LoggingMiddleware(
            app=mock_app,
            log_request_body=True,
            max_body_size=10,  # Very small limit
        )

        # Mock large request body
        large_body = b'{"test": "very large data that exceeds the limit"}'
        mock_request.body = AsyncMock(return_value=large_body)
        mock_request.headers = {"content-type": "application/json"}

        response = await middleware.dispatch(mock_request, mock_call_next)

        # Verify body was not logged due to size
        mock_logger.info.assert_called()
        call_args = mock_logger.info.call_args
        request_data = call_args[1]["extra"]["request_data"]
        assert "body_size" in request_data
        assert "body too large" in request_data["body"]


class TestLoggingMiddlewareIntegration:
    """Integration tests for LoggingMiddleware with real FastAPI app."""
    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.logger")
    async def test_integration_successful_request(
        self, mock_logger, test_app_with_logging
    ):
        """Test logging middleware integration with successful request."""
        from fastapi.testclient import TestClient

        client = TestClient(test_app_with_logging)
        response = client.get("/test")

        assert response.status_code == 200
        assert response.json() == {"message": "test"}

        # Verify logging occurred
        mock_logger.info.assert_called()

    @pytest.mark.asyncio
    @patch("src.middleware.logging_middleware.logger")
    async def test_integration_error_request(self, mock_logger, test_app_with_logging):
        """Test logging middleware integration with error request."""
        from fastapi.testclient import TestClient

        client = TestClient(test_app_with_logging)

        # The test client will raise the exception, so we need to catch it
        # to verify the middleware logging behavior
        try:
            response = client.get("/error")
            # If we get here, the error was handled by FastAPI's error middleware
            assert response.status_code == 500
        except ValueError:
            # The ValueError is propagated through the test client
            # This is expected behavior in test environment
            pass

        # Verify error logging occurred
        mock_logger.error.assert_called()
