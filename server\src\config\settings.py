# src/config/settings.py
"""Application Configuration Settings.

This module defines the configuration settings for the Ultimate Electrical Designer
backend application using Pydantic Settings. It handles environment variables,
database connections, security settings, and application parameters.

The settings support both development and production environments with automatic
fallback configurations and validation.
"""

# backend/config/settings.py
import os
from pathlib import Path

from pydantic import (
    Field,
)
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables or a .env file.
    Pydantic's BaseSettings handles the loading and validation.
    """

    # --- Application Metadata ---
    APP_NAME: str = "Heat Tracing Design Application"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "An engineering application for industrial electrical design, calculations, and management."
    APP_ROOT: str = Field(
        default="./src/",
        description="Root directory of the application.",
    )

    # Environment can be 'development', 'testing', 'production'
    ENVIRONMENT: str = Field(
        default="development",
        pattern="^(development|testing|production)$",
        description="Application environment (development, testing, production)",
    )
    DEBUG: bool = Field(
        default=True,
        description="Enable debug features (e.g., debug endpoints, detailed error messages)",
    )

    APP_PORT: int = Field(
        default=8000,
        description="Port to run the FastAPI server on.",
    )

    # --- Database Configuration ---
    DATABASE_URL: str | None = Field(
        default=None,
        description="Primary database connection string (PostgreSQL)."
        " If not provided, SQLite fallback will be used.",
    )
    SQLITE_DATABASE_PATH: str = Field(
        default="app_dev.db",  # Default filename for SQLite database file
        description="Filename for the SQLite database file for offline mode or fallback.",
    )
    DB_ECHO: bool = Field(
        default=False,
        description="Set to True to log all SQLAlchemy statements to stdout (useful for debugging DB queries).",
    )

    # --- Caching & Rate Limiting (Redis) ---
    REDIS_ENABLED: bool = Field(
        default=False,
        description="Set to True to enable Redis for caching and/or rate limiting.",
    )
    REDIS_URL: str | None = Field(
        default=None,
        description="Redis connection URL (e.g., 'redis://localhost:6379/0'). Required if REDIS_ENABLED is True.",
    )
    RATE_LIMIT_ENABLED: bool = Field(
        default=True,  # Default to True for security, can be disabled for dev
        description="Set to True to enable API rate limiting.",
    )
    RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE: int = Field(
        default=100,  # Default rate limit
        description="Default number of requests allowed per minute for rate limiting.",
    )

    # --- Security ---
    # !!! IMPORTANT !!!
    # This key MUST be changed in production. Generate a strong, random string (e.g., using secrets.token_urlsafe(32)).
    # It should be loaded from an environment variable in production deployments.
    SECRET_KEY: str = Field(
        default="&1DeDbTClEtRDKZI8zrb5FXN8o%Bgvqr",
        min_length=32,
        description="Secret key for cryptographic operations (e.g., JWT signing). Must be strong and secret.",
    )
    JWT_ALGORITHM: str = Field(
        default="HS256",
        description="Algorithm used for signing JSON Web Tokens (JWTs)."
    )
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30,
        description="Expiration time for JWT access tokens in minutes."
    )

    # --- Logging ---
    # Supported levels: CRITICAL, ERROR, WARNING, INFO, DEBUG, NOTSET
    LOG_LEVEL: str = Field(
        default="INFO",
        pattern="^(CRITICAL|ERROR|WARNING|INFO|DEBUG|NOTSET)$",
        description="Minimum logging level for the application.",
    )

    # --- Computed Properties ---
    @property
    def effective_database_url(self) -> str:
        """Returns the effective database URL to use, with automatic fallback to SQLite.

        Logic:
        1. If DATABASE_URL is provided and environment is production, use it
        2. If DATABASE_URL is provided and environment is development/testing, try it but fallback to SQLite
        3. If DATABASE_URL is None, use SQLite
        """
        if self.DATABASE_URL:
            # For production, always use the provided DATABASE_URL
            if self.ENVIRONMENT == "production":
                return self.DATABASE_URL
            # For development/testing, we'll try the DATABASE_URL but the database layer will handle fallback
            return self.DATABASE_URL

        # Fallback to SQLite
        return self._get_sqlite_url()

    def _get_sqlite_url(self) -> str:
        """Generate SQLite database URL with proper path handling."""
        # Get the project root directory (server directory)
        # This works whether we're running from server/ or server/src/
        current_file = Path(__file__).resolve()  # This file is in server/src/config/
        server_dir = current_file.parent.parent.parent  # Go up to server/

        # Construct the database path in server/data/
        data_dir = server_dir / "data"
        data_dir.mkdir(parents=True, exist_ok=True)

        db_path = data_dir / self.SQLITE_DATABASE_PATH

        return f"sqlite:///{db_path.as_posix()}"

    # --- Pydantic Settings Configuration ---
    # This tells Pydantic's BaseSettings how to load the settings.
    model_config = SettingsConfigDict(
        env_file=".env",  # Look for a .env file in the current directory
        env_file_encoding="utf-8",  # Encoding for the .env file
        case_sensitive=True,  # Environment variable names are case-sensitive
        extra="ignore",  # Ignore extra environment variables not defined here
    )


# Instantiate the settings object to make it globally available
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings instance."""
    return settings
